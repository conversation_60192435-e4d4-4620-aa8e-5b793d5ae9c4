import {Sequelize} from 'sequelize';
import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import {fileURLToPath} from 'url';

const dialect = process.env.DB_DIALECT || 'mysql';
let sequelize;

console.log(`Initializing database connection with dialect: ${dialect}`);

if (dialect === 'mysql') {
    const dbName = process.env.MYSQL_DATABASE;
    const dbUser = process.env.MYSQL_USER;
    const dbPassword = process.env.MYSQL_PASSWORD;
    const dbHost = process.env.MYSQL_HOST;

    if (!dbName || !dbUser || !dbHost) {
        console.error('Missing required MySQL environment variables: MYSQL_DATABASE, MYSQL_USER, MYSQL_HOST');
        process.exit(1);
    }

    const mysqlOptions = {
        host: dbHost,
        dialect: 'mysql',
        logging: process.env.NODE_ENV === 'development'
    };

    sequelize = new Sequelize(dbName, dbUser, dbPassword, mysqlOptions);

} else if (dialect === 'sqlite') {
    const storagePath = process.env.SQLITE_STORAGE;

    if (!storagePath) {
        console.error('Missing required SQLite environment variable: SQLITE_STORAGE');
        process.exit(1);
    }

    const sqliteOptions = {
        dialect: 'sqlite',
        storage: storagePath,
        logging: false,
        define: {
            // Enable foreign key constraints for SQLite
            freezeTableName: true
        }
    };
    sequelize = new Sequelize(sqliteOptions);

} else if (dialect === 'postgres') {
    const dbName = process.env.POSTGRES_DATABASE;
    const dbUser = process.env.POSTGRES_USER;
    const dbPassword = process.env.POSTGRES_PASSWORD;
    const dbHost = process.env.POSTGRES_HOST;
    const dbPort = process.env.POSTGRES_PORT || 5432;

    if (!dbName || !dbUser || !dbHost) {
        console.error('Missing required PostgreSQL environment variables: POSTGRES_DATABASE, POSTGRES_USER, POSTGRES_HOST');
        process.exit(1);
    }

    const postgresOptions = {
        host: dbHost,
        port: dbPort,
        dialect: 'postgres',
        logging: process.env.NODE_ENV === 'development',
        dialectOptions: {
            ssl: process.env.POSTGRES_SSL === 'true' ? {
                require: true,
                rejectUnauthorized: false
            } : false
        }
    };

    sequelize = new Sequelize(dbName, dbUser, dbPassword, postgresOptions);
} else {
    console.error(`Unsupported DB_DIALECT: ${dialect}`);
    process.exit(1);
}


if (sequelize) {
    sequelize.authenticate()
        .then(() => {
            console.log('Database connection established successfully.');
        })
        .catch(err => {
            console.error('Unable to connect to the database:', err);
            process.exit(1);
        });
} else {
    console.error('Sequelize instance was not created.');
    process.exit(1);
}

export {sequelize};