# Enhanced Flow Navigation System - Migration Guide

## Overview

The enhanced flow navigation system introduces flexible navigation capabilities that allow for complex branching conversations, loops, and cross-flow navigation. This guide covers the migration from the old system to the new one.

## Key Improvements

### Before (Old System)
- ❌ Only linear flow execution
- ❌ Limited to flow-to-flow switching
- ❌ No internal step jumping
- ❌ No loops or retry mechanisms
- ❌ Complex scenarios required multiple duplicate flows

### After (New System)
- ✅ Flexible internal step navigation
- ✅ Cross-flow navigation with step precision
- ✅ Loop and retry mechanisms
- ✅ Unconditional jumps
- ✅ Backward compatibility with old format

## Migration Steps

### 1. Update Existing Flows (Optional)

The system maintains **full backward compatibility**. Existing flows will continue to work without changes, but you'll see deprecation warnings.

To migrate existing flows, run the migration script:

```bash
node migrations/migrate-conditional-config.js
```

To rollback (if needed):
```bash
node migrations/migrate-conditional-config.js rollback
```

### 2. New Syntax Overview

#### Old Format (Still Supported)
```javascript
conditionConfig: {
    condition: { type: 'age_check', operator: '<', value: 18 },
    successFlowId: 'other-flow-id',  // or 'continue'
    failFlowId: 'continue'
}
```

#### New Format (Recommended)
```javascript
conditionConfig: {
    condition: { type: 'age_check', operator: '<', value: 18 },
    successAction: { goto: 'other-flow-id' },
    failAction: { goto: 'continue' }
}
```

### 3. Goto Syntax Reference

| Pattern | Example | Description |
|---------|---------|-------------|
| `continue` | `"continue"` | Move to next step in current flow |
| `#step` | `"#15"` | Jump to step 15 in current flow |
| `flowId` | `"flow-uuid-123"` | Jump to beginning of another flow |
| `flowId#step` | `"flow-uuid-123#15"` | Jump to step 15 in another flow |

## Common Migration Patterns

### Pattern 1: Simple Flow Switching
```javascript
// OLD
successFlowId: 'help-flow-id'

// NEW
successAction: { goto: 'help-flow-id' }
```

### Pattern 2: Continue Current Flow
```javascript
// OLD
successFlowId: 'continue'

// NEW
successAction: { goto: 'continue' }
```

### Pattern 3: Age Verification with Retry
```javascript
// NEW - Jump to error handling step, then back to retry
{
    step: 1,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: { type: 'age_check', operator: '<', value: 18 },
        successAction: { goto: '#10' }, // Jump to age error step
        failAction: { goto: 'continue' }
    }
},
// ... other steps ...
{
    step: 10,
    content: 'You must be 18+. Try again? {да|нет}'
},
{
    step: 11,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: { type: 'yes_no', value: 'да' },
        successAction: { goto: '#0' }, // Return to beginning
        failAction: { goto: '#15' }    // Jump to end
    }
}
```

## New Condition Type: always_true

For unconditional navigation:

```javascript
{
    step: 5,
    content: 'Processing...',
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: { type: 'always_true' },
        successAction: { goto: '#10' }, // Always executed
        failAction: { goto: 'continue' } // Never executed
    }
}
```

## Testing Your Migration

### 1. Run Navigation Tests
```bash
node scripts/simple-navigation-test.js
```

### 2. Test Backward Compatibility
```bash
node scripts/test-backward-compatibility.js
```

### 3. Test Advanced Flows
```bash
node scripts/test-enhanced-navigation.js
```

## Best Practices

### 1. Flow Design
- **Plan your navigation paths** before implementing
- **Use meaningful step numbers** (e.g., 10, 20, 30 for major sections)
- **Document complex navigation logic** in comments

### 2. Error Handling
- **Always provide fallback paths** for failed conditions
- **Test all navigation branches** thoroughly
- **Use `always_true` sparingly** - prefer explicit conditions

### 3. Performance
- **Avoid deep nesting** of conditional switches
- **Minimize cross-flow jumps** when possible
- **Use internal navigation** for simple branching

### 4. Maintenance
- **Migrate to new format** when updating flows
- **Use consistent step numbering** across your flows
- **Document navigation logic** in flow descriptions

## Troubleshooting

### Common Issues

1. **"Invalid step number" Error**
   - Check that target step exists in the flow
   - Ensure step numbers are integers

2. **"Target flow not found" Error**
   - Verify flow IDs are correct
   - Ensure target flows exist in database

3. **Infinite Loops**
   - Review navigation paths for circular references
   - Add exit conditions to loops

4. **Unexpected Navigation**
   - Check condition logic and test data
   - Verify goto syntax is correct

### Debug Tips

Enable detailed logging to trace navigation:
```javascript
// Check logs for navigation decisions
logger.info('🧭 Executing navigation: type=...', navigationAction);
```

## Examples

See the following files for complete examples:
- `seeders/advancedFlowSeeder.js` - Advanced navigation patterns
- `tests/enhancedConditionalSwitching.test.js` - Test scenarios
- `docs/conditional-flow-switching.md` - Detailed API documentation

## Support

If you encounter issues during migration:
1. Check the logs for detailed error messages
2. Verify your goto syntax using the validation function
3. Test with simple scenarios first
4. Refer to the examples in the codebase

The enhanced navigation system is designed to be powerful yet intuitive. Take advantage of its flexibility to create more engaging and efficient conversation flows!
