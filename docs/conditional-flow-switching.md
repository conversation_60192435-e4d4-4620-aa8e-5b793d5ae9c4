# Enhanced Flow Navigation System

This document describes the enhanced flow navigation system that allows the bot to dynamically navigate between steps and flows based on user responses, enabling complex branching conversation logic.

## Overview

The enhanced navigation system provides flexible control over conversation flow with support for:

- **Internal step jumping**: Navigate to any step within the current flow
- **Flow switching**: Switch to different flows at any step
- **Conditional branching**: Different navigation paths based on user responses
- **Loops and cycles**: Return to previous steps for retry scenarios
- **Unconditional jumps**: Force navigation regardless of conditions

This is useful for scenarios like:
- Age verification with different paths for different age groups
- Document verification with retry loops
- Complex multi-step processes with branching logic
- Error handling and recovery flows

## Key Components

### 1. Flow Accessibility Control

Flows now have an `isStartingFlow` boolean field that determines whether a flow can be used as an initial flow by the AI flow determination system.

- `isStartingFlow: true` - Flow can be selected as initial flow
- `isStartingFlow: false` - Flow can only be accessed through conditional switching

### 2. Conditional Switch Messages

A new message type `conditional_switch` allows flows to evaluate conditions and switch to different flows.

#### Message Structure

```javascript
{
    content: "Checking your eligibility...",
    step: 2,
    messageType: 'conditional_switch',
    conditionConfig: {
        // Condition to check against current user response
        condition: {
            type: 'age_check',
            operator: '<',
            value: 18
        },

        // Flow IDs for success/fail cases
        successFlowId: 'uuid-of-success-flow', // or 'continue' or omitted
        failFlowId: 'uuid-of-fail-flow' // or 'continue' or omitted
    }
}
```

### 3. Condition Types

#### Age Check
```javascript
{
    type: 'age_check',
    operator: '<', // <, >, <=, >=, ==
    value: 18
}
```

#### Yes/No Check
```javascript
{
    type: 'yes_no',
    value: 'да' // or 'нет'
}
```

#### Equals Check
```javascript
{
    type: 'equals',
    value: 'expected_value'
}
```

#### Contains Check
```javascript
{
    type: 'contains',
    value: 'search_term'
}
```

#### Numeric Compare
```javascript
{
    type: 'numeric_compare',
    operator: '>', // <, >, <=, >=, ==, !=
    value: 100
}
```

## Implementation Examples

### Example 1: Age Verification

```javascript
// Main flow with age verification
const mainFlow = {
    name: 'Service Application',
    isStartingFlow: true,
    messages: [
        {
            content: 'What is your age? {numeric}',
            step: 0
        },
        {
            content: 'Checking age requirements...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    operator: '<',
                    value: 18
                },
                successFlowId: 'age-restriction-flow-id', // If under 18
                failFlowId: 'continue' // If 18 or older, continue current flow
            }
        },
        {
            content: 'Great! You meet the age requirement. Do you have required documents? {да|нет}',
            step: 2
        },
        {
            content: 'Please proceed to window #5',
            step: 3
        }
    ]
};

// Age restriction flow (not a starting flow)
const ageRestrictionFlow = {
    name: 'Age Restriction',
    isStartingFlow: false,
    messages: [
        {
            content: 'This service is only available for users 18 and older. Please return when you reach the required age.',
            step: 0
        }
    ]
};
```

### Example 2: Yes/No Branching

```javascript
{
    content: 'Do you have the required documents?',
    step: 1,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: {
            type: 'yes_no',
            value: 'да'
        },
        targetFlowId: 'documents-available-flow-id'
    }
}
// If user answers "да" → switch to documents-available-flow
// If user answers "нет" → continue current flow
```

### Example 3: Continue Current Flow

```javascript
{
    content: 'Are you a new customer?',
    step: 1,
    messageType: 'conditional_switch',
    conditionConfig: {
        condition: {
            type: 'yes_no',
            value: 'да'
        },
        targetFlowId: 'continue'
    }
}
// If user answers "да" → continue current flow
// If user answers "нет" → continue current flow
// This example shows how to use conditional logic without switching flows
```

## Database Schema Changes

### Flows Table
- Added `isStartingFlow` BOOLEAN field (default: true)

### FlowMessages Table
- Added `messageType` STRING field (default: 'regular')
- Added `conditionConfig` JSON field for conditional configuration

## API Usage

The API endpoints have been enhanced to support conditional flow switching mechanics.

### Flow Management

#### Create Flow
```javascript
POST /api/flow
{
    "name": "Service Application",
    "description": "Main service application flow",
    "isStartingFlow": true,  // NEW: Controls flow accessibility
    "aiPrompt": "Evaluate if user has required documents...",
    "successMessage": "All requirements met!",
    "failureMessage": "Missing requirements."
}

// Response includes all flow fields including isStartingFlow
```

#### Update Flow
```javascript
PUT /api/flow/:flowId
{
    "name": "Updated Flow Name",
    "isStartingFlow": false  // NEW: Can update accessibility
}
```

#### Get Starting Flows Only
```javascript
GET /api/flow/starting-flows
// Returns only flows where isStartingFlow: true
```

### Flow Message Management

#### Create Regular Message
```javascript
POST /api/flow/:flowId/message
{
    "content": "What is your age? {numeric}",
    "step": 0,
    "messageType": "regular"  // NEW: Optional, defaults to 'regular'
}
```

#### Create Conditional Switch Message
```javascript
POST /api/flow/:flowId/message
{
    "content": "Checking age requirements...",
    "step": 1,
    "messageType": "conditional_switch",  // NEW: Enables flow switching
    "conditionConfig": {  // NEW: Condition configuration
        "condition": {
            "type": "age_check",
            "operator": "<",
            "value": 18
        },
        "targetFlowId": "age-restriction-flow-id"
    }
}
```

#### Update Message with Conditional Logic
```javascript
PUT /api/flow/:flowId/message/:messageId
{
    "messageType": "conditional_switch",
    "conditionConfig": {
        "condition": {
            "type": "yes_no",
            "value": "да"
        },
        "targetFlowId": "continue"  // Continue current flow if condition met
    }
}
```

### Validation

#### Validate Condition Configuration
```javascript
POST /api/flow/validate-condition
{
    "conditionConfig": {
        "condition": {
            "type": "age_check",
            "operator": ">=",
            "value": 18
        },
        "targetFlowId": "existing-flow-id"
    }
}

// Response:
{
    "valid": true,
    "message": "Condition configuration is valid"
}
// OR
{
    "valid": false,
    "error": "Referenced flow with ID xyz does not exist"
}
```

### Dialog Processing

#### Starting a Dialog
```javascript
POST /api/dialog/start
// Returns dialog with initial flow (only isStartingFlow: true flows are considered)
```

#### Processing Messages
```javascript
POST /api/dialog/message
{
    "dialogId": "dialog-uuid",
    "message": "user response"
}

// Response may include:
{
    "dialogId": "dialog-uuid",
    "message": "bot response",
    "step": 0,
    "completed": false,
    "flowSwitched": true,  // Indicates flow was switched
    "newFlowId": "new-flow-uuid"
}
```

### API Validation Errors

The API now includes comprehensive validation with detailed error messages:

#### Flow Creation/Update Errors
```javascript
// Invalid isStartingFlow
{
    "error": "isStartingFlow must be a boolean value"
}
```

#### Message Creation/Update Errors
```javascript
// Invalid message type
{
    "error": "messageType must be one of: regular, conditional_switch"
}

// Missing condition config for conditional message
{
    "error": "conditionConfig is required for conditional_switch messages"
}

// Invalid condition type
{
    "error": "Condition type must be one of: age_check, yes_no, equals, contains, numeric_compare"
}

// Invalid operator
{
    "error": "Operator for age_check must be one of: >, <, >=, <=, ==, ="
}

// Missing flow reference
{
    "error": "Referenced flow with ID abc-123 does not exist"
}

// Invalid condition structure
{
    "error": "Condition must specify either stepReference or field"
}
```

## Migration

Run the migration to add the new database fields:

```bash
npx sequelize-cli db:migrate
```

## Testing

Run the conditional flow switching tests:

```bash
npm test tests/conditionalFlowSwitching.test.js
```

## Best Practices

1. **Flow Design**: Design flows with clear entry and exit points
2. **Condition Logic**: Keep conditions simple and testable
3. **Error Handling**: Always provide fallback flows or messages
4. **Testing**: Test all conditional paths thoroughly
5. **Documentation**: Document complex conditional logic clearly

## Troubleshooting

### Common Issues

1. **Flow Not Found**: Ensure target flow IDs are correct and flows exist
2. **Condition Not Met**: Verify condition configuration and test data
3. **Infinite Loops**: Avoid circular flow references
4. **Missing Data**: Ensure referenced steps contain required data

### Debugging

Enable debug logging to trace condition evaluation:

```javascript
// Check condition evaluator logs
logger.info('Condition evaluation result: ...', { condition, result });
```
