{"name": "uslugibot", "version": "1.0.0", "description": "", "main": "db.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "setup-conditional-flows": "node scripts/setupConditionalFlowSwitching.js", "seed-flows": "node seeders/flowSeeder.js", "example-conditional-flows": "node examples/createConditionalFlow.js", "test-flow-api": "npm test tests/flowApiRoutes.test.js"}, "type": "module", "private": true, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "openai": "^4.98.0", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "winston": "^3.17.0"}}