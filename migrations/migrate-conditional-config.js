import { FlowMessage } from '../models/index.js';
import logger from '../utils/logger.js';

/**
 * Migration script to convert old conditionConfig format to new format
 * Old format: { successFlowId, failFlowId }
 * New format: { successAction: { goto }, failAction: { goto } }
 */
async function migrateConditionalConfig() {
    try {
        logger.info('Starting conditional config migration...');

        // Find all conditional switch messages
        const conditionalMessages = await FlowMessage.findAll({
            where: {
                messageType: 'conditional_switch'
            }
        });

        logger.info(`Found ${conditionalMessages.length} conditional switch messages to migrate`);

        let migratedCount = 0;
        let skippedCount = 0;

        for (const message of conditionalMessages) {
            const { conditionConfig } = message;

            if (!conditionConfig) {
                logger.warn(`Message ${message.id} has no conditionConfig, skipping`);
                skippedCount++;
                continue;
            }

            // Check if already in new format
            if (conditionConfig.successAction && conditionConfig.failAction) {
                logger.info(`Message ${message.id} already in new format, skipping`);
                skippedCount++;
                continue;
            }

            // Check if in old format
            if (conditionConfig.successFlowId !== undefined || conditionConfig.failFlowId !== undefined) {
                // Convert to new format
                const newConditionConfig = {
                    condition: conditionConfig.condition,
                    successAction: {
                        goto: conditionConfig.successFlowId || 'continue'
                    },
                    failAction: {
                        goto: conditionConfig.failFlowId || 'continue'
                    }
                };

                // Update the message
                await message.update({
                    conditionConfig: newConditionConfig
                });

                logger.info(`Migrated message ${message.id} from old format to new format`);
                migratedCount++;
            } else {
                logger.warn(`Message ${message.id} has unknown conditionConfig format, skipping`);
                skippedCount++;
            }
        }

        logger.info(`Migration completed: ${migratedCount} migrated, ${skippedCount} skipped`);
        return {
            total: conditionalMessages.length,
            migrated: migratedCount,
            skipped: skippedCount
        };

    } catch (error) {
        logger.error(`Migration failed: ${error.message}`);
        throw error;
    }
}

/**
 * Rollback migration - convert new format back to old format
 * This is for testing purposes or if rollback is needed
 */
async function rollbackConditionalConfig() {
    try {
        logger.info('Starting conditional config rollback...');

        // Find all conditional switch messages
        const conditionalMessages = await FlowMessage.findAll({
            where: {
                messageType: 'conditional_switch'
            }
        });

        logger.info(`Found ${conditionalMessages.length} conditional switch messages to rollback`);

        let rolledBackCount = 0;
        let skippedCount = 0;

        for (const message of conditionalMessages) {
            const { conditionConfig } = message;

            if (!conditionConfig) {
                logger.warn(`Message ${message.id} has no conditionConfig, skipping`);
                skippedCount++;
                continue;
            }

            // Check if in new format
            if (conditionConfig.successAction && conditionConfig.failAction) {
                // Convert back to old format
                const oldConditionConfig = {
                    condition: conditionConfig.condition,
                    successFlowId: conditionConfig.successAction.goto === 'continue' ? 'continue' : conditionConfig.successAction.goto,
                    failFlowId: conditionConfig.failAction.goto === 'continue' ? 'continue' : conditionConfig.failAction.goto
                };

                // Update the message
                await message.update({
                    conditionConfig: oldConditionConfig
                });

                logger.info(`Rolled back message ${message.id} from new format to old format`);
                rolledBackCount++;
            } else {
                logger.info(`Message ${message.id} already in old format or unknown format, skipping`);
                skippedCount++;
            }
        }

        logger.info(`Rollback completed: ${rolledBackCount} rolled back, ${skippedCount} skipped`);
        return {
            total: conditionalMessages.length,
            rolledBack: rolledBackCount,
            skipped: skippedCount
        };

    } catch (error) {
        logger.error(`Rollback failed: ${error.message}`);
        throw error;
    }
}

// Export functions
export { migrateConditionalConfig, rollbackConditionalConfig };

// If run directly, execute migration
if (import.meta.url === `file://${process.argv[1]}`) {
    const command = process.argv[2];
    
    if (command === 'rollback') {
        rollbackConditionalConfig()
            .then(result => {
                console.log('Rollback result:', result);
                process.exit(0);
            })
            .catch(error => {
                console.error('Rollback failed:', error);
                process.exit(1);
            });
    } else {
        migrateConditionalConfig()
            .then(result => {
                console.log('Migration result:', result);
                process.exit(0);
            })
            .catch(error => {
                console.error('Migration failed:', error);
                process.exit(1);
            });
    }
}
