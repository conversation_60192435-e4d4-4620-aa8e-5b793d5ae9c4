'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add isStartingFlow field to flows table
    await queryInterface.addColumn('flows', 'isStartingFlow', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this flow can be used as an initial/starting flow'
    });

    // Add messageType field to flow_messages table
    await queryInterface.addColumn('flow_messages', 'messageType', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'regular',
      comment: 'Type of message: regular or conditional_switch'
    });

    // Add conditionConfig field to flow_messages table
    await queryInterface.addColumn('flow_messages', 'conditionConfig', {
      type: Sequelize.JSON,
      allowNull: true,
      comment: 'Configuration for conditional flow switching (only for conditional_switch type)'
    });

    // Add check constraint for messageType (if supported by the database)
    try {
      await queryInterface.addConstraint('flow_messages', {
        fields: ['messageType'],
        type: 'check',
        where: {
          messageType: ['regular', 'conditional_switch']
        },
        name: 'flow_messages_messageType_check'
      });
    } catch (error) {
      // Some databases might not support check constraints
      console.log('Check constraint not added (database might not support it):', error.message);
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove check constraint (if it was added)
    try {
      await queryInterface.removeConstraint('flow_messages', 'flow_messages_messageType_check');
    } catch (error) {
      // Constraint might not exist
      console.log('Check constraint not removed (might not exist):', error.message);
    }

    // Remove conditionConfig field from flow_messages table
    await queryInterface.removeColumn('flow_messages', 'conditionConfig');

    // Remove messageType field from flow_messages table
    await queryInterface.removeColumn('flow_messages', 'messageType');

    // Remove isStartingFlow field from flows table
    await queryInterface.removeColumn('flows', 'isStartingFlow');
  }
};
