import logger from '../utils/logger.js';

/**
 * Service for evaluating conditions in conditional flow switching
 */
class ConditionEvaluator {
    constructor() {
        logger.info('Condition evaluator service initialized');
    }

    /**
     * Evaluate a condition against user response
     * @param {Object} condition - Condition configuration
     * @param {string} userResponse - User's processed response from mask
     * @returns {boolean} - True if condition is met, false otherwise
     */
    evaluateCondition(condition, userResponse) {
        try {
            const { type, operator, value } = condition;

            logger.info(`🔍 CONDITION EVAL: type=${type}, operator=${operator}, value=${value}, userResponse="${userResponse}" (type: ${typeof userResponse})`);

            // Evaluate based on condition type
            switch (type) {
                case 'equals':
                    return this.evaluateEquals(userResponse, value);
                case 'age_check':
                    return this.evaluateAgeCheck(userResponse, operator, value);
                case 'yes_no':
                    return this.evaluateYesNo(userResponse, value);
                case 'contains':
                    return this.evaluateContains(userResponse, value);
                case 'numeric_compare':
                    return this.evaluateNumericCompare(userResponse, operator, value);
                case 'always_true':
                    return this.evaluateAlwaysTrue();
                default:
                    logger.warn(`Unknown condition type: ${type}`);
                    return false;
            }
        } catch (error) {
            logger.error(`Error evaluating condition: ${error.message}`);
            return false;
        }
    }

    /**
     * Evaluate equals condition
     * @param {string} targetValue - Value to check
     * @param {string} expectedValue - Expected value
     * @returns {boolean}
     */
    evaluateEquals(targetValue, expectedValue) {
        return String(targetValue).toLowerCase() === String(expectedValue).toLowerCase();
    }

    /**
     * Evaluate age check condition
     * @param {string|number} targetValue - Age value (processed response)
     * @param {string} operator - Comparison operator (>, <, >=, <=, ==)
     * @param {number} expectedAge - Expected age
     * @returns {boolean}
     */
    evaluateAgeCheck(targetValue, operator, expectedAge) {
        const age = parseInt(targetValue);
        if (isNaN(age)) {
            logger.warn(`❌ Invalid age value: ${targetValue} (could not parse to number)`);
            return false;
        }

        let result;
        switch (operator) {
            case '>':
                result = age > expectedAge;
                break;
            case '<':
                result = age < expectedAge;
                break;
            case '>=':
                result = age >= expectedAge;
                break;
            case '<=':
                result = age <= expectedAge;
                break;
            case '==':
            case '=':
                result = age === expectedAge;
                break;
            default:
                logger.warn(`Unknown age check operator: ${operator}`);
                return false;
        }

        logger.info(`✅ Age check: ${age} ${operator} ${expectedAge} = ${result}`);
        return result;
    }

    /**
     * Evaluate yes/no condition
     * @param {string} targetValue - Response value
     * @param {string} expectedValue - Expected value ('да' or 'нет')
     * @returns {boolean}
     */
    evaluateYesNo(targetValue, expectedValue) {
        const normalizedTarget = String(targetValue).toLowerCase();
        const normalizedExpected = String(expectedValue).toLowerCase();
        
        return normalizedTarget === normalizedExpected;
    }

    /**
     * Evaluate contains condition
     * @param {string} targetValue - Value to check
     * @param {string} searchValue - Value to search for
     * @returns {boolean}
     */
    evaluateContains(targetValue, searchValue) {
        return String(targetValue).toLowerCase().includes(String(searchValue).toLowerCase());
    }

    /**
     * Evaluate numeric comparison condition
     * @param {string|number} targetValue - Numeric value to check
     * @param {string} operator - Comparison operator
     * @param {number} expectedValue - Expected numeric value
     * @returns {boolean}
     */
    evaluateNumericCompare(targetValue, operator, expectedValue) {
        const numValue = parseFloat(targetValue);
        if (isNaN(numValue)) {
            logger.warn(`Invalid numeric value: ${targetValue}`);
            return false;
        }

        switch (operator) {
            case '>':
                return numValue > expectedValue;
            case '<':
                return numValue < expectedValue;
            case '>=':
                return numValue >= expectedValue;
            case '<=':
                return numValue <= expectedValue;
            case '==':
            case '=':
                return numValue === expectedValue;
            case '!=':
                return numValue !== expectedValue;
            default:
                logger.warn(`Unknown numeric comparison operator: ${operator}`);
                return false;
        }
    }


}

// Create and export a singleton instance
const conditionEvaluator = new ConditionEvaluator();
export default conditionEvaluator;
