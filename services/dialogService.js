import {v4 as uuidv4} from 'uuid';
import logger from '../utils/logger.js';
import {Dialog, Message, Flow} from '../models/index.js';
import aiService from './aiService.js';
import flowDialogService from './flowDialogService.js';

class DialogService {
    constructor() {
        logger.info('Dialog service initialized');
    }

    /**
     * Start a new dialog
     * @returns {Promise<Object>} - Created dialog
     */
    async startDialog() {
        try {
            // Create new dialog without flow
            const dialog = await Dialog.create({
                id: uuidv4(),
                flowId: null,
                currentStep: 0,
                collectedData: {},
                completed: false
            });

            logger.info(`Started new dialog ${dialog.id} without flow`);

            // Add first bot message to dialog
            const welcomeMessage = "Здравствуйте! Я виртуальный помощник МФЦ. Чем я могу вам помочь? Опишите, пожалуйста, какую услугу вы хотели бы получить.";

            await Message.create({
                id: uuidv4(),
                dialogId: dialog.id,
                role: 'assistant',
                content: welcomeMessage,
                timestamp: new Date()
            });

            return {
                dialogId: dialog.id,
                message: welcomeMessage
            };
        } catch (error) {
            logger.error(`Error starting dialog: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process user message in a dialog
     * @param {string} dialogId - Dialog ID
     * @param {string} userMessage - User's message
     * @returns {Promise<Object>} - Next step info
     */
    async processMessage(dialogId, userMessage) {
        try {
            // Get dialog
            const dialog = await Dialog.findByPk(dialogId);
            if (!dialog) {
                throw new Error(`Dialog ${dialogId} not found`);
            }

            // If dialog already has a flow, use flowDialogService
            if (dialog.flowId) {
                return await flowDialogService.processMessage(dialogId, userMessage);
            }

            // Add user message to dialog
            await Message.create({
                id: uuidv4(),
                dialogId,
                role: 'user',
                content: userMessage,
                timestamp: new Date()
            });

            // If no flow yet, determine flow with AI
            // Get all available starting flows
            const flows = await Flow.findAll({
                where: {
                    isStartingFlow: true
                }
            });

            if (flows.length === 0) {
                throw new Error('No flows available in the system');
            }

            // Determine flow with AI
            const flowDetermination = await aiService.determineFlow(userMessage, flows);

            // If AI couldn't determine a flow or confidence is too low
            if (!flowDetermination.flowId || flowDetermination.confidence < 0.5) {
                // Ask for more information
                const responseMessage = flowDetermination.explanation +
                    "\n\nПожалуйста, уточните, какую услугу вы хотели бы получить?";

                await Message.create({
                    id: uuidv4(),
                    dialogId,
                    role: 'assistant',
                    content: responseMessage,
                    timestamp: new Date()
                });

                return {
                    dialogId,
                    message: responseMessage,
                    flowDetermined: false
                };
            }

            // Flow determined, update dialog
            await dialog.update({flowId: flowDetermination.flowId});

            // Start flow dialog
            const flowStart = await flowDialogService.startDialog(flowDetermination.flowId, dialog);

            return {
                dialogId,
                message: flowStart.message,
                flowDetermined: true,
                flowId: flowDetermination.flowId
            };
        } catch (error) {
            logger.error(`Error processing message in dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get all messages from a dialog
     * @param {string} dialogId - Dialog ID
     * @returns {Promise<Array>} - Dialog messages
     */
    async getMessages(dialogId) {
        try {
            const messages = await Message.findAll({
                where: {dialogId},
                order: [['timestamp', 'ASC']]
            });

            return messages;
        } catch (error) {
            logger.error(`Error getting messages for dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get formatted messages for AI
     * @param {string} dialogId - Dialog ID
     * @returns {Promise<Array>} - Formatted messages
     */
    async getFormattedMessages(dialogId) {
        const messages = await this.getMessages(dialogId);

        return messages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
    }

    /**
     * Add a message to a dialog
     * @param {string} dialogId - Dialog ID
     * @param {string} role - Message role (user/assistant)
     * @param {string} content - Message content
     * @returns {Promise<Object>} - Created message
     */
    async addMessage(dialogId, role, content) {
        try {
            const message = await Message.create({
                id: uuidv4(),
                dialogId,
                role,
                content,
                timestamp: new Date()
            });

            return message;
        } catch (error) {
            logger.error(`Error adding message to dialog ${dialogId}: ${error.message}`);
            throw error;
        }
    }

    /**
     * Complete a dialog and evaluate results
     * @param {Object} dialog - Dialog object
     * @returns {Promise<Object>} - Completion result
     */
    async completeDialog(dialog) {
        try {
            // Get flow for AI prompt
            const flow = await Flow.findByPk(dialog.flowId);

            // Validate collected data before completing
            const validationErrors = this.validateCollectedData(dialog.collectedData);
            if (validationErrors.length > 0) {
                // Add validation error message
                const errorMessage = `Обнаружены ошибки в предоставленных данных: ${validationErrors.join(', ')}. Пожалуйста, уточните информацию.`;

                await this.addMessage(dialog.id, 'assistant', errorMessage);

                return {
                    dialogId: dialog.id,
                    message: errorMessage,
                    completed: false,
                    validationErrors
                };
            }

            // Get all messages from dialog
            const messages = await Message.findAll({
                where: {dialogId: dialog.id},
                order: [['timestamp', 'ASC']]
            });

            // Format dialog for AI evaluation
            const formattedDialog = messages.map(m => ({
                role: m.role,
                content: m.content
            }));

            // Evaluate dialog with AI using flow.aiPrompt
            let result = "pass"; // Default to pass

            if (flow.aiPrompt) {
                // Call AI to evaluate dialog
                result = await aiService.evaluateDialog(formattedDialog, flow.aiPrompt, dialog.collectedData);
            }

            // Determine final message based on result
            const finalMessage = result === "pass" ?
                (flow.successMessage || "Все необходимые документы в наличии. Пожалуйста, возьмите талончик на услугу.") :
                (flow.failureMessage || "Не все необходимые документы в наличии. Пожалуйста, подготовьте все документы и вернитесь.");

            // Update dialog as completed
            await dialog.update({
                completed: true,
                completionResult: result
            });

            // Add final message
            await this.addMessage(dialog.id, 'assistant', finalMessage);

            return {
                dialogId: dialog.id,
                message: finalMessage,
                completed: true,
                result
            };
        } catch (error) {
            logger.error(`Error completing dialog: ${error.message}`);
            throw error;
        }
    }

    /**
     * Validate collected data for a dialog
     * @param {Object} collectedData - Collected data from dialog
     * @returns {Array} - Array of validation error messages
     */
    validateCollectedData(collectedData) {
        const errors = [];

        // Check each step's data
        Object.entries(collectedData).forEach(([step, data]) => {
            // Check for numeric mask
            if (data.question.includes('{numeric}')) {
                const numericValue = parseInt(data.processed);
                if (isNaN(numericValue)) {
                    errors.push(`Ожидалось числовое значение, получено: "${data.answer}"`);
                }
            }

            // Check for yes/no answers
            if (data.question.includes('{да|нет}')) {
                if (data.answer.toLowerCase().includes('забыл') ||
                    data.answer.toLowerCase().includes('нет') ||
                    data.answer.toLowerCase().includes('не имею')) {
                    errors.push(`Требуется наличие документа: "${data.question.replace(/\{[^}]+\}/g, '')}"`);
                }
            }
        });

        return errors;
    }
}

// Create and export a singleton instance
const dialogService = new DialogService();
export default dialogService;