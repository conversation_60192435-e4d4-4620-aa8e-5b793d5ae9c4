import {OpenAI} from 'openai';
import logger from '../utils/logger.js';

// Инициализация клиента OpenAI с параметрами из .env
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_API_BASE_URL || undefined
});

class AIService {
    constructor() {
        this.model = process.env.OPENAI_MODEL || 'gpt-4o';
        logger.info(`AI Service initialized with model: ${this.model}`);
    }

    /**
     * Определяет подходящий поток для запроса пользователя
     * @param {string} userQuery - Запрос пользователя
     * @param {Array} availableFlows - Доступные потоки
     * @returns {Promise<Object>} - Выбранный поток и объяснение
     */
    async determineFlow(userQuery, availableFlows) {
        try {
            // Формируем контекст с описанием доступных потоков
            const flowDescriptions = availableFlows.map(flow =>
                `ID: ${flow.id}\nНазвание: (${flow.id}) ${flow.name}\nОписание: ${flow.description || 'Нет описания'}`
            ).join('\n\n');

            // Формируем промпт для модели
            const prompt = `Ты - помощник в МФЦ (многофункциональном центре). 
Твоя задача - определить, какую услугу хочет получить пользователь, и выбрать соответствующий поток обслуживания.

Доступные потоки услуг:
${flowDescriptions}

Запрос пользователя: "${userQuery}"

Выбери наиболее подходящий поток для этого запроса. Ответ дай в формате JSON:
{
  "flowId": "ID выбранного потока",
  "confidence": число от 0 до 1, отражающее уверенность в выборе,
  "explanation": "Краткое объяснение, почему выбран этот поток"
}

Если ни один поток не подходит, верни:
{
  "flowId": null,
  "confidence": 0,
  "explanation": "Объяснение, почему ни один поток не подходит"
}`;

            // Вызываем API OpenAI
            const response = await openai.chat.completions.create({
                model: this.model,
                messages: [{role: 'user', content: prompt}],
                temperature: 0.3, // Низкая температура для более детерминированных ответов
            });

            // Получаем ответ модели
            let content = response.choices[0].message.content;

            // Удаляем теги <think> и их содержимое
            content = content.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

            // Ищем JSON в ответе (может быть обернут в тройные бэктики)
            const jsonMatch = content.match(/```(?:json)?([\s\S]*?)```/) || [null, content];
            const jsonContent = jsonMatch[1].trim();

            logger.info(`Cleaned AI response: ${jsonContent}`);

            // Парсим JSON из ответа
            try {
                const result = JSON.parse(jsonContent);
                logger.info(`AI determined flow: ${result.flowId} with confidence ${result.confidence}`);
                return result;
            } catch (error) {
                logger.error(`Failed to parse AI response as JSON: ${content}`);
                // Возвращаем запасной вариант, если не удалось распарсить JSON
                return {
                    flowId: null,
                    confidence: 0,
                    explanation: "Не удалось определить подходящий поток"
                };
            }
        } catch (error) {
            logger.error(`Error in AI flow determination: ${error.message}`);
            throw error;
        }
    }

    /**
     * Оценивает диалог и определяет результат
     * @param {Array} dialog - Форматированный диалог
     * @param {string} prompt - AI prompt из потока
     * @param {Object} collectedData - Собранные данные
     * @returns {Promise<string>} - Результат оценки ("pass" или "fail")
     */
    async evaluateDialog(dialog, prompt, collectedData) {
        try {
            // Форматируем диалог для отправки в API
            const dialogText = dialog.map(msg =>
                `${msg.role.toUpperCase()}: ${msg.content}`
            ).join('\n');

            // Форматируем собранные данные
            const dataText = Object.entries(collectedData).map(([key, value]) =>
                `${key}: Вопрос: "${value.question}", Ответ: "${value.answer}", Обработано: "${value.processed}"`
            ).join('\n');

            // Формируем промпт для модели
            const evaluationPrompt = `${prompt}

Диалог:
${dialogText}

Собранные данные:
${dataText}

Оцени, соответствует ли пользователь требованиям для получения услуги.
Ответь только "pass", если все требования соблюдены, или "fail", если какие-то требования не соблюдены.`;

            // Вызываем API OpenAI
            const response = await openai.chat.completions.create({
                model: this.model,
                messages: [{role: 'user', content: evaluationPrompt}],
                temperature: 0.1, // Очень низкая температура для детерминированных ответов
            });

            // Получаем ответ модели и нормализуем его
            let result = response.choices[0].message.content.trim().toLowerCase();

            // Удаляем теги <think> и их содержимое
            result = result.replace(/<think>[\s\S]*?<\/think>/g, '').trim();

            logger.info(`AI evaluation result: ${result}`);

            // Проверяем, содержит ли ответ "pass" или "fail"
            if (result.includes('pass')) {
                return "pass";
            } else if (result.includes('fail')) {
                return "fail";
            } else {
                // Если ответ неоднозначный, возвращаем fail для безопасности
                logger.warn(`Ambiguous AI evaluation result: ${result}, defaulting to fail`);
                return "fail";
            }
        } catch (error) {
            logger.error(`Error in AI dialog evaluation: ${error.message}`);
            // В случае ошибки возвращаем fail для безопасности
            return "fail";
        }
    }
}

// Создаем и экспортируем синглтон
const aiService = new AIService();
export default aiService;