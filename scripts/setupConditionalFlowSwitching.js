#!/usr/bin/env node

import { sequelize } from '../db.js';
import logger from '../utils/logger.js';
import seedFlows from '../seeders/flowSeeder.js';
import setupConditionalFlows from './setupConditionalFlows.js';

/**
 * Complete setup script for conditional flow switching feature
 */
async function setupConditionalFlowSwitching() {
    try {
        logger.info('Starting conditional flow switching setup...');

        // 1. Sync database schema (this will apply migrations)
        logger.info('Syncing database schema...');
        await sequelize.sync({ alter: true });
        logger.info('Database schema synced successfully');

        // 2. Clear existing flows and seed new ones
        logger.info('Seeding flows...');
        await seedFlows();
        logger.info('Flows seeded successfully');

        // 3. Set up conditional flow references
        logger.info('Setting up conditional flow references...');
        await setupConditionalFlows();
        logger.info('Conditional flow references set up successfully');

        logger.info('✅ Conditional flow switching setup completed successfully!');
        
        // Display summary
        console.log('\n📋 Setup Summary:');
        console.log('- Database schema updated with new fields');
        console.log('- Example flows created with conditional switching');
        console.log('- Flow references configured');
        console.log('\n🚀 You can now test the conditional flow switching feature!');
        console.log('\nExample test scenarios:');
        console.log('1. Start a dialog and enter age < 18 to see flow switching');
        console.log('2. Try the "Консультация по документам" flow for yes/no branching');
        console.log('3. Use the "Оформление СНИЛС с проверкой возраста" flow for age verification');

    } catch (error) {
        logger.error(`Setup failed: ${error.message}`);
        console.error('❌ Setup failed:', error.message);
        throw error;
    }
}

// Run setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    setupConditionalFlowSwitching()
        .then(() => {
            logger.info('Setup completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            logger.error('Setup failed:', error);
            process.exit(1);
        });
}

export default setupConditionalFlowSwitching;
