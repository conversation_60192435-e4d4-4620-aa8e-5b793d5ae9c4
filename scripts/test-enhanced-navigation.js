import { sequelize } from '../db.js';
import { Flow, FlowMessage, Dialog } from '../models/index.js';
import flowDialogService from '../services/flowDialogService.js';
import navigationService from '../services/navigationService.js';
import seedAdvancedFlows from '../seeders/advancedFlowSeeder.js';
import logger from '../utils/logger.js';

/**
 * Test script for enhanced navigation system
 */
async function testEnhancedNavigation() {
    try {
        logger.info('🚀 Starting enhanced navigation system test...');

        // Initialize database
        await sequelize.sync({ force: true });
        logger.info('✅ Database synchronized');

        // Seed advanced flows
        await seedAdvancedFlows();
        logger.info('✅ Advanced flows seeded');

        // Test 1: Navigation Service Parsing
        logger.info('\n📋 Test 1: Navigation Service Parsing');
        testNavigationParsing();

        // Test 2: Find and test a flow with internal navigation
        logger.info('\n📋 Test 2: Internal Navigation Flow Test');
        await testInternalNavigation();

        // Test 3: Test flow switching
        logger.info('\n📋 Test 3: Flow Switching Test');
        await testFlowSwitching();

        logger.info('\n🎉 All tests completed successfully!');

    } catch (error) {
        logger.error(`❌ Test failed: ${error.message}`);
        throw error;
    }
}

/**
 * Test navigation service parsing
 */
function testNavigationParsing() {
    const testCases = [
        { input: 'continue', expected: { type: 'continue', flowId: null, step: null } },
        { input: '#5', expected: { type: 'step', flowId: null, step: 5 } },
        { input: 'flow-123', expected: { type: 'flow', flowId: 'flow-123', step: 0 } },
        { input: 'flow-123#7', expected: { type: 'flow_step', flowId: 'flow-123', step: 7 } }
    ];

    for (const testCase of testCases) {
        try {
            const result = navigationService.parseGoto(testCase.input);
            const matches = JSON.stringify(result) === JSON.stringify(testCase.expected);
            
            if (matches) {
                logger.info(`  ✅ "${testCase.input}" → ${JSON.stringify(result)}`);
            } else {
                logger.error(`  ❌ "${testCase.input}" → Expected: ${JSON.stringify(testCase.expected)}, Got: ${JSON.stringify(result)}`);
            }
        } catch (error) {
            logger.error(`  ❌ "${testCase.input}" → Error: ${error.message}`);
        }
    }

    // Test validation
    const validInputs = ['continue', '#5', 'flow-123', 'flow-123#7'];
    const invalidInputs = ['', '#abc', 'flow#abc', null];

    for (const input of validInputs) {
        const isValid = navigationService.validateGoto(input);
        if (isValid) {
            logger.info(`  ✅ Validation: "${input}" is valid`);
        } else {
            logger.error(`  ❌ Validation: "${input}" should be valid but was rejected`);
        }
    }

    for (const input of invalidInputs) {
        const isValid = navigationService.validateGoto(input);
        if (!isValid) {
            logger.info(`  ✅ Validation: "${input}" correctly rejected`);
        } else {
            logger.error(`  ❌ Validation: "${input}" should be invalid but was accepted`);
        }
    }
}

/**
 * Test internal navigation within a flow
 */
async function testInternalNavigation() {
    try {
        // Find the "Анкета с проверками" flow
        const flow = await Flow.findOne({
            where: { name: 'Анкета с проверками' },
            include: [{ model: FlowMessage, order: [['step', 'ASC']] }]
        });

        if (!flow) {
            logger.error('  ❌ Test flow "Анкета с проверками" not found');
            return;
        }

        logger.info(`  📝 Found flow: ${flow.name} with ${flow.FlowMessages.length} messages`);

        // Create a test dialog
        const dialog = await Dialog.create({
            flowId: flow.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });

        // Start the dialog
        await flowDialogService.startDialog(flow.id, dialog);
        logger.info('  ✅ Dialog started');

        // Test scenario: Under 18 user (should jump to step 10)
        logger.info('  🧪 Testing under 18 scenario...');
        const result1 = await flowDialogService.processMessage(dialog.id, '16');
        
        if (result1.step === 10) {
            logger.info(`  ✅ Correctly jumped to step ${result1.step} for under 18 user`);
        } else {
            logger.error(`  ❌ Expected step 10, got step ${result1.step}`);
        }

        // Clean up
        await dialog.destroy();

    } catch (error) {
        logger.error(`  ❌ Internal navigation test failed: ${error.message}`);
    }
}

/**
 * Test flow switching
 */
async function testFlowSwitching() {
    try {
        // Find the main flow that switches to helper flow
        const mainFlow = await Flow.findOne({
            where: { name: 'Основной процесс с переходами' },
            include: [{ model: FlowMessage, order: [['step', 'ASC']] }]
        });

        const helperFlow = await Flow.findOne({
            where: { name: 'Помощник по документам' }
        });

        if (!mainFlow || !helperFlow) {
            logger.error('  ❌ Required flows not found');
            return;
        }

        logger.info(`  📝 Found flows: ${mainFlow.name} and ${helperFlow.name}`);

        // Create a test dialog
        const dialog = await Dialog.create({
            flowId: mainFlow.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });

        // Start the dialog
        await flowDialogService.startDialog(mainFlow.id, dialog);
        logger.info('  ✅ Dialog started');

        // Test scenario: User wants help (should switch to helper flow)
        logger.info('  🧪 Testing flow switching scenario...');
        const result = await flowDialogService.processMessage(dialog.id, 'да');
        
        if (result.flowSwitched && result.newFlowId === helperFlow.id) {
            logger.info(`  ✅ Successfully switched to helper flow: ${result.newFlowId}`);
        } else {
            logger.error(`  ❌ Flow switching failed. FlowSwitched: ${result.flowSwitched}, NewFlowId: ${result.newFlowId}`);
        }

        // Clean up
        await dialog.destroy();

    } catch (error) {
        logger.error(`  ❌ Flow switching test failed: ${error.message}`);
    }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testEnhancedNavigation()
        .then(() => {
            logger.info('🏁 Test script completed');
            process.exit(0);
        })
        .catch(error => {
            logger.error(`💥 Test script failed: ${error.message}`);
            process.exit(1);
        });
}

export default testEnhancedNavigation;
