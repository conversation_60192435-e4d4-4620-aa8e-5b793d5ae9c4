import {sequelize} from '../db.js';
import seedFlows from '../seeders/flowSeeder.js';
import logger from '../utils/logger.js';

async function runSeeders() {
    try {
        logger.info('Starting database seeding...');

        // Синхронизация моделей с базой данных
        await sequelize.sync({force: true});
        logger.info('Database synchronized');

        // Запуск сидеров
        //await seedFlows();

        logger.info('Database seeding completed successfully');
        process.exit(0);
    } catch (error) {
        logger.error(`Database seeding failed: ${error.message}`);
        logger.error(error.stack);
        process.exit(1);
    }
}

// Запуск сидеров
runSeeders();