import {Flow, FlowMessage} from '../models/index.js';
import logger from '../utils/logger.js';

/**
 * <PERSON>ript to set up conditional flow switching after flows are created
 * This script updates the conditionConfig with actual flow IDs
 */
async function setupConditionalFlows() {
    try {
        logger.info('Setting up conditional flow switching...');

        // Get flow IDs
        const ageRestrictionFlow = await Flow.findOne({
            where: { name: 'Возрастное ограничение' }
        });

        const snilsFlow = await Flow.findOne({
            where: { name: 'Оформление СНИЛС с проверкой возраста' }
        });

        if (!ageRestrictionFlow) {
            logger.error('Age restriction flow not found');
            return;
        }

        if (!snilsFlow) {
            logger.error('СНИЛС flow not found');
            return;
        }

        // Update the conditional switch message in СНИЛС flow
        const conditionalMessage = await FlowMessage.findOne({
            where: {
                flowId: snilsFlow.id,
                step: 2,
                messageType: 'conditional_switch'
            }
        });

        if (conditionalMessage) {
            const updatedConfig = {
                ...conditionalMessage.conditionConfig,
                successFlowId: ageRestrictionFlow.id // Switch to age restriction flow if under 18
            };

            await conditionalMessage.update({
                conditionConfig: updatedConfig
            });

            logger.info(`Updated conditional switch in СНИЛС flow to reference age restriction flow: ${ageRestrictionFlow.id}`);
        } else {
            logger.warn('Conditional switch message not found in СНИЛС flow');
        }

        logger.info('Conditional flow setup completed successfully');
    } catch (error) {
        logger.error(`Error setting up conditional flows: ${error.message}`);
        throw error;
    }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    setupConditionalFlows()
        .then(() => {
            logger.info('Setup completed');
            process.exit(0);
        })
        .catch((error) => {
            logger.error('Setup failed:', error);
            process.exit(1);
        });
}

export default setupConditionalFlows;
