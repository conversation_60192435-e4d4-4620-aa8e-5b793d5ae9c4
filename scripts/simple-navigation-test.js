import navigationService from '../services/navigationService.js';
import logger from '../utils/logger.js';

/**
 * Simple test for navigation service without database
 */
function testNavigationParsing() {
    logger.info('🚀 Testing navigation service parsing...');

    const testCases = [
        { input: 'continue', expected: { type: 'continue', flowId: null, step: null } },
        { input: '#5', expected: { type: 'step', flowId: null, step: 5 } },
        { input: 'flow-123', expected: { type: 'flow', flowId: 'flow-123', step: 0 } },
        { input: 'flow-123#7', expected: { type: 'flow_step', flowId: 'flow-123', step: 7 } }
    ];

    let passed = 0;
    let failed = 0;

    for (const testCase of testCases) {
        try {
            const result = navigationService.parseGoto(testCase.input);
            const matches = JSON.stringify(result) === JSON.stringify(testCase.expected);
            
            if (matches) {
                logger.info(`  ✅ "${testCase.input}" → ${JSON.stringify(result)}`);
                passed++;
            } else {
                logger.error(`  ❌ "${testCase.input}" → Expected: ${JSON.stringify(testCase.expected)}, Got: ${JSON.stringify(result)}`);
                failed++;
            }
        } catch (error) {
            logger.error(`  ❌ "${testCase.input}" → Error: ${error.message}`);
            failed++;
        }
    }

    // Test validation
    logger.info('\n🔍 Testing validation...');
    const validInputs = ['continue', '#5', 'flow-123', 'flow-123#7'];
    const invalidInputs = ['', '#abc', 'flow#abc', null];

    for (const input of validInputs) {
        const isValid = navigationService.validateGoto(input);
        if (isValid) {
            logger.info(`  ✅ Validation: "${input}" is valid`);
            passed++;
        } else {
            logger.error(`  ❌ Validation: "${input}" should be valid but was rejected`);
            failed++;
        }
    }

    for (const input of invalidInputs) {
        const isValid = navigationService.validateGoto(input);
        if (!isValid) {
            logger.info(`  ✅ Validation: "${input}" correctly rejected`);
            passed++;
        } else {
            logger.error(`  ❌ Validation: "${input}" should be invalid but was accepted`);
            failed++;
        }
    }

    logger.info(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
        logger.info('🎉 All tests passed!');
        return true;
    } else {
        logger.error('💥 Some tests failed!');
        return false;
    }
}

// Run the test
const success = testNavigationParsing();
process.exit(success ? 0 : 1);
