import { sequelize } from '../db.js';
import { Flow, FlowMessage, Dialog } from '../models/index.js';
import flowDialogService from '../services/flowDialogService.js';
import logger from '../utils/logger.js';
import { v4 as uuidv4 } from 'uuid';

/**
 * Test backward compatibility with old conditionConfig format
 */
async function testBackwardCompatibility() {
    try {
        logger.info('🚀 Testing backward compatibility...');

        // Initialize database
        await sequelize.sync({ force: true });
        logger.info('✅ Database synchronized');

        // Create test flow with old format
        const testFlow = await Flow.create({
            id: uuidv4(),
            name: 'Backward Compatibility Test',
            description: 'Test flow for backward compatibility',
            isStartingFlow: true,
            aiPrompt: 'Test prompt',
            successMessage: 'Success!',
            failureMessage: 'Failed!'
        });

        // Create messages with OLD format
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'What is your age? {numeric}',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Checking age (OLD FORMAT)...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    operator: '<',
                    value: 18
                },
                // OLD FORMAT
                successFlowId: 'continue', // If under 18, continue
                failFlowId: 'continue' // If 18+, continue
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Age check completed.',
            step: 2
        });

        logger.info('✅ Test flow with old format created');

        // Create test dialog
        const dialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });

        // Start the dialog
        await flowDialogService.startDialog(testFlow.id, dialog);
        logger.info('✅ Dialog started');

        // Test with under 18 user
        logger.info('🧪 Testing with age 16 (old format)...');
        const result1 = await flowDialogService.processMessage(dialog.id, '16');
        
        if (result1.step === 2) {
            logger.info('✅ Old format works correctly - continued to step 2');
        } else {
            logger.error(`❌ Expected step 2, got step ${result1.step}`);
        }

        // Reset dialog for second test
        await dialog.update({ currentStep: 0 });
        await flowDialogService.startDialog(testFlow.id, dialog);

        // Test with 18+ user
        logger.info('🧪 Testing with age 25 (old format)...');
        const result2 = await flowDialogService.processMessage(dialog.id, '25');
        
        if (result2.step === 2) {
            logger.info('✅ Old format works correctly - continued to step 2');
        } else {
            logger.error(`❌ Expected step 2, got step ${result2.step}`);
        }

        // Now test NEW format
        logger.info('\n🆕 Testing NEW format...');

        // Create another flow with NEW format
        const newFlow = await Flow.create({
            id: uuidv4(),
            name: 'New Format Test',
            description: 'Test flow for new format',
            isStartingFlow: true,
            aiPrompt: 'Test prompt',
            successMessage: 'Success!',
            failureMessage: 'Failed!'
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: newFlow.id,
            content: 'What is your age? {numeric}',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: newFlow.id,
            content: 'Checking age (NEW FORMAT)...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    operator: '<',
                    value: 18
                },
                // NEW FORMAT
                successAction: { goto: '#5' }, // If under 18, jump to step 5
                failAction: { goto: 'continue' } // If 18+, continue
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: newFlow.id,
            content: 'You are 18 or older.',
            step: 2
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: newFlow.id,
            content: 'Process completed.',
            step: 3
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: newFlow.id,
            content: 'You are under 18.',
            step: 5
        });

        // Create new dialog for new format test
        const newDialog = await Dialog.create({
            id: uuidv4(),
            flowId: newFlow.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });

        // Start the dialog
        await flowDialogService.startDialog(newFlow.id, newDialog);

        // Test with under 18 user (should jump to step 5)
        logger.info('🧪 Testing with age 16 (new format - should jump to step 5)...');
        const result3 = await flowDialogService.processMessage(newDialog.id, '16');
        
        if (result3.step === 5) {
            logger.info('✅ New format works correctly - jumped to step 5');
        } else {
            logger.error(`❌ Expected step 5, got step ${result3.step}`);
        }

        // Reset dialog for second test
        await newDialog.update({ currentStep: 0 });
        await flowDialogService.startDialog(newFlow.id, newDialog);

        // Test with 18+ user (should continue to step 2)
        logger.info('🧪 Testing with age 25 (new format - should continue to step 2)...');
        const result4 = await flowDialogService.processMessage(newDialog.id, '25');
        
        if (result4.step === 2) {
            logger.info('✅ New format works correctly - continued to step 2');
        } else {
            logger.error(`❌ Expected step 2, got step ${result4.step}`);
        }

        logger.info('\n🎉 Backward compatibility test completed successfully!');

    } catch (error) {
        logger.error(`❌ Backward compatibility test failed: ${error.message}`);
        throw error;
    }
}

// Run the test
testBackwardCompatibility()
    .then(() => {
        logger.info('🏁 Test completed');
        process.exit(0);
    })
    .catch(error => {
        logger.error(`💥 Test failed: ${error.message}`);
        process.exit(1);
    });
