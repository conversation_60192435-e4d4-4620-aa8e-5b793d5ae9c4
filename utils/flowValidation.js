import { Flow } from '../models/index.js';
import logger from './logger.js';

/**
 * Validation utilities for flow management API
 */

/**
 * Valid condition types
 */
const VALID_CONDITION_TYPES = [
    'age_check',
    'yes_no', 
    'equals',
    'contains',
    'numeric_compare'
];

/**
 * Valid operators for different condition types
 */
const VALID_OPERATORS = {
    age_check: ['>', '<', '>=', '<=', '==', '='],
    numeric_compare: ['>', '<', '>=', '<=', '==', '=', '!=']
};

/**
 * Valid message types
 */
const VALID_MESSAGE_TYPES = ['regular', 'conditional_switch'];

/**
 * Valid logic operators for multiple conditions
 */
const VALID_LOGIC_OPERATORS = ['AND', 'OR'];

/**
 * Validate isStartingFlow parameter
 * @param {any} isStartingFlow - Value to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateIsStartingFlow(isStartingFlow) {
    if (isStartingFlow === undefined || isStartingFlow === null) {
        return { isValid: true }; // Optional field
    }
    
    if (typeof isStartingFlow !== 'boolean') {
        return { 
            isValid: false, 
            error: 'isStartingFlow must be a boolean value' 
        };
    }
    
    return { isValid: true };
}

/**
 * Validate message type
 * @param {string} messageType - Message type to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateMessageType(messageType) {
    if (messageType === undefined || messageType === null) {
        return { isValid: true }; // Optional field, defaults to 'regular'
    }
    
    if (!VALID_MESSAGE_TYPES.includes(messageType)) {
        return {
            isValid: false,
            error: `messageType must be one of: ${VALID_MESSAGE_TYPES.join(', ')}`
        };
    }
    
    return { isValid: true };
}

/**
 * Validate a single condition
 * @param {Object} condition - Condition object to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateCondition(condition) {
    if (!condition || typeof condition !== 'object') {
        return { isValid: false, error: 'Condition must be an object' };
    }

    const { type, operator, value } = condition;

    // Validate condition type
    if (!type || !VALID_CONDITION_TYPES.includes(type)) {
        return {
            isValid: false,
            error: `Condition type must be one of: ${VALID_CONDITION_TYPES.join(', ')}`
        };
    }

    // Validate operator for types that require it
    if (['age_check', 'numeric_compare'].includes(type)) {
        if (!operator || !VALID_OPERATORS[type].includes(operator)) {
            return {
                isValid: false,
                error: `Operator for ${type} must be one of: ${VALID_OPERATORS[type].join(', ')}`
            };
        }
    }

    // Validate value is provided
    if (value === undefined || value === null) {
        return { isValid: false, error: 'Condition value is required' };
    }

    // Validate numeric values for age_check and numeric_compare
    if (['age_check', 'numeric_compare'].includes(type)) {
        if (typeof value !== 'number' || isNaN(value)) {
            return {
                isValid: false,
                error: `Value for ${type} must be a valid number`
            };
        }
    }

    // Validate yes_no values
    if (type === 'yes_no') {
        if (!['да', 'нет'].includes(value)) {
            return {
                isValid: false,
                error: 'Value for yes_no condition must be "да" or "нет"'
            };
        }
    }

    return { isValid: true };
}

/**
 * Validate condition configuration for conditional_switch messages
 * @param {Object} conditionConfig - Condition configuration to validate
 * @returns {Object} - {isValid: boolean, error?: string}
 */
export function validateConditionConfig(conditionConfig) {
    if (!conditionConfig || typeof conditionConfig !== 'object') {
        return { isValid: false, error: 'conditionConfig must be an object' };
    }

    const { condition, successFlowId, failFlowId } = conditionConfig;

    // Must have condition
    if (!condition) {
        return {
            isValid: false,
            error: 'conditionConfig must specify "condition"'
        };
    }

    // Validate condition
    const conditionValidation = validateCondition(condition);
    if (!conditionValidation.isValid) {
        return conditionValidation;
    }

    // Validate successFlowId (if provided, must be string or "continue")
    if (successFlowId !== undefined && successFlowId !== null && typeof successFlowId !== 'string') {
        return {
            isValid: false,
            error: 'successFlowId must be a string (flow ID or "continue") or omitted'
        };
    }

    // Validate failFlowId (if provided, must be string or "continue")
    if (failFlowId !== undefined && failFlowId !== null && typeof failFlowId !== 'string') {
        return {
            isValid: false,
            error: 'failFlowId must be a string (flow ID or "continue") or omitted'
        };
    }

    return { isValid: true };
}

/**
 * Validate that referenced flow ID exists in the database
 * @param {Object} conditionConfig - Condition configuration
 * @returns {Promise<Object>} - {isValid: boolean, error?: string}
 */
export async function validateFlowReferences(conditionConfig) {
    const { targetFlowId } = conditionConfig;

    try {
        // Skip validation if targetFlowId is "continue"
        if (targetFlowId === 'continue') {
            return { isValid: true };
        }

        // Check targetFlowId exists
        const targetFlow = await Flow.findByPk(targetFlowId);
        if (!targetFlow) {
            return {
                isValid: false,
                error: `Referenced flow with ID ${targetFlowId} does not exist`
            };
        }

        return { isValid: true };
    } catch (error) {
        logger.error(`Error validating flow references: ${error.message}`);
        return {
            isValid: false,
            error: 'Failed to validate flow references'
        };
    }
}

/**
 * Validate flow message data for creation/update
 * @param {Object} messageData - Message data to validate
 * @param {boolean} isUpdate - Whether this is an update operation
 * @returns {Promise<Object>} - {isValid: boolean, error?: string}
 */
export async function validateFlowMessageData(messageData, isUpdate = false) {
    const { content, step, messageType, conditionConfig } = messageData;
    
    // Validate required fields for creation
    if (!isUpdate) {
        if (!content) {
            return { isValid: false, error: 'content is required' };
        }
        if (step === undefined) {
            return { isValid: false, error: 'step is required' };
        }
    }
    
    // Validate step is a number
    if (step !== undefined && (typeof step !== 'number' || step < 0)) {
        return { isValid: false, error: 'step must be a non-negative number' };
    }
    
    // Validate message type
    const messageTypeValidation = validateMessageType(messageType);
    if (!messageTypeValidation.isValid) {
        return messageTypeValidation;
    }
    
    // Validate conditional configuration
    if (messageType === 'conditional_switch') {
        if (!conditionConfig) {
            return {
                isValid: false,
                error: 'conditionConfig is required for conditional_switch messages'
            };
        }
        
        const configValidation = validateConditionConfig(conditionConfig);
        if (!configValidation.isValid) {
            return configValidation;
        }
        
        // Validate flow references exist
        const flowRefValidation = await validateFlowReferences(conditionConfig);
        if (!flowRefValidation.isValid) {
            return flowRefValidation;
        }
    } else if (conditionConfig) {
        return {
            isValid: false,
            error: 'conditionConfig can only be provided for conditional_switch messages'
        };
    }
    
    return { isValid: true };
}
