#!/usr/bin/env node

/**
 * Example script demonstrating how to create flows with conditional switching
 * using the updated API endpoints
 */

import axios from 'axios';

// Configure your API base URL
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000/api';

/**
 * Create a flow using the API
 */
async function createFlow(flowData) {
    try {
        const response = await axios.post(`${API_BASE_URL}/flow`, flowData);
        console.log('✅ Created flow:', response.data.name);
        return response.data;
    } catch (error) {
        console.error('❌ Error creating flow:', error.response?.data?.error || error.message);
        throw error;
    }
}

/**
 * Create a flow message using the API
 */
async function createFlowMessage(flowId, messageData) {
    try {
        const response = await axios.post(`${API_BASE_URL}/flow/${flowId}/message`, messageData);
        console.log(`✅ Created ${messageData.messageType || 'regular'} message at step ${messageData.step}`);
        return response.data;
    } catch (error) {
        console.error('❌ Error creating message:', error.response?.data?.error || error.message);
        throw error;
    }
}

/**
 * Validate a condition configuration
 */
async function validateCondition(conditionConfig) {
    try {
        const response = await axios.post(`${API_BASE_URL}/flow/validate-condition`, {
            conditionConfig
        });
        console.log('✅ Condition validation:', response.data.message);
        return response.data.valid;
    } catch (error) {
        console.error('❌ Condition validation failed:', error.response?.data?.error || error.message);
        return false;
    }
}

/**
 * Main example function
 */
async function createConditionalFlowExample() {
    try {
        console.log('🚀 Creating flows with conditional switching...\n');

        // 1. Create main flow (starting flow)
        const mainFlow = await createFlow({
            name: 'Age Verification Service',
            description: 'Service with age verification and conditional flow switching',
            isStartingFlow: true,
            aiPrompt: 'Check if user meets age requirements and has necessary documents',
            successMessage: 'All requirements met! Please proceed to the service window.',
            failureMessage: 'Requirements not met. Please ensure you meet all criteria.'
        });

        // 2. Create age restriction flow (not a starting flow)
        const ageRestrictionFlow = await createFlow({
            name: 'Age Restriction Notice',
            description: 'Flow for users who do not meet age requirements',
            isStartingFlow: false,
            successMessage: 'Please return when you meet the age requirement.',
            failureMessage: 'Service not available.'
        });

        console.log('\n📝 Creating flow messages...\n');

        // 3. Create messages for main flow
        
        // Step 0: Ask for age
        await createFlowMessage(mainFlow.flowId, {
            content: 'Welcome! To proceed with this service, please tell me your age. {numeric}',
            step: 0,
            messageType: 'regular'
        });

        // Step 1: Conditional switch based on age
        const conditionConfig = {
            condition: {
                type: 'age_check',
                operator: '<',
                value: 18
            },
            successFlowId: ageRestrictionFlow.flowId, // If under 18, go to restriction flow
            failFlowId: 'continue' // If 18+, continue current flow
        };

        // Validate condition before creating message
        const isValid = await validateCondition(conditionConfig);
        if (!isValid) {
            throw new Error('Condition configuration is invalid');
        }

        await createFlowMessage(mainFlow.flowId, {
            content: 'Let me check if you meet our requirements...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig
        });

        // Step 2: Continue main flow (only reached if age >= 18)
        await createFlowMessage(mainFlow.flowId, {
            content: 'Great! You meet the age requirement. Do you have a valid ID with you? {да|нет}',
            step: 2,
            messageType: 'regular'
        });

        // Step 3: Final step
        await createFlowMessage(mainFlow.flowId, {
            content: 'Perfect! You have all the requirements. Please take a number and wait for your turn.',
            step: 3,
            messageType: 'regular'
        });

        // 4. Create message for age restriction flow
        await createFlowMessage(ageRestrictionFlow.flowId, {
            content: 'This service requires you to be 18 years or older. You can return once you reach the required age. Is there anything else I can help you with today?',
            step: 0,
            messageType: 'regular'
        });

        console.log('\n🎉 Successfully created flows with conditional switching!');
        console.log('\n📋 Summary:');
        console.log(`- Main Flow ID: ${mainFlow.flowId}`);
        console.log(`- Age Restriction Flow ID: ${ageRestrictionFlow.flowId}`);
        console.log('- Conditional switching configured for age verification');
        console.log('\n🧪 Test the flow by:');
        console.log('1. Starting a dialog');
        console.log('2. Entering an age < 18 to see flow switching');
        console.log('3. Entering an age >= 18 to continue main flow');

    } catch (error) {
        console.error('\n💥 Example failed:', error.message);
        process.exit(1);
    }
}

/**
 * Example of creating a flow with multiple conditions
 */
async function createMultiConditionFlowExample() {
    try {
        console.log('\n🔄 Creating flow with multiple conditions...\n');

        const eligibilityFlow = await createFlow({
            name: 'Service Eligibility Check',
            description: 'Flow with multiple condition checks',
            isStartingFlow: true
        });

        const eligibleFlow = await createFlow({
            name: 'Eligible for Service',
            description: 'Flow for eligible users',
            isStartingFlow: false
        });

        const notEligibleFlow = await createFlow({
            name: 'Not Eligible',
            description: 'Flow for non-eligible users',
            isStartingFlow: false
        });

        // Create messages
        await createFlowMessage(eligibilityFlow.flowId, {
            content: 'What is your age? {numeric}',
            step: 0
        });

        await createFlowMessage(eligibilityFlow.flowId, {
            content: 'Do you have the required documents? {да|нет}',
            step: 1
        });

        // Simple condition - check if user has documents
        const documentCheckConfig = {
            condition: {
                type: 'yes_no',
                value: 'да'
            },
            targetFlowId: eligibleFlow.flowId
        };

        await validateCondition(documentCheckConfig);

        await createFlowMessage(eligibilityFlow.flowId, {
            content: 'Checking if you have documents...',
            step: 2,
            messageType: 'conditional_switch',
            conditionConfig: documentCheckConfig
        });

        // Add message for when user doesn't have documents (continues current flow)
        await createFlowMessage(eligibilityFlow.flowId, {
            content: 'Unfortunately, you need to have the required documents. Please return when you have them.',
            step: 3,
            messageType: 'regular'
        });

        // Add messages to target flows
        await createFlowMessage(eligibleFlow.flowId, {
            content: 'Excellent! You have all the required documents. Please proceed to the service window.',
            step: 0,
            messageType: 'regular'
        });

        await createFlowMessage(notEligibleFlow.flowId, {
            content: 'Unfortunately, you do not meet the requirements. Please contact our support for more information.',
            step: 0,
            messageType: 'regular'
        });

        console.log('✅ Document check flow created successfully!');

    } catch (error) {
        console.error('❌ Document check example failed:', error.message);
    }
}

// Run examples
if (import.meta.url === `file://${process.argv[1]}`) {
    createConditionalFlowExample()
        .then(() => createMultiConditionFlowExample())
        .then(() => {
            console.log('\n🎯 All examples completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('Examples failed:', error);
            process.exit(1);
        });
}

export { createConditionalFlowExample, createMultiConditionFlowExample };
