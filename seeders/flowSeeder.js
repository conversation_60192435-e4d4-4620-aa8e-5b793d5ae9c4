import {v4 as uuidv4} from 'uuid';
import {Flow, FlowMessage} from '../models/index.js';
import logger from '../utils/logger.js';

/**
 * Создает поток и его сообщения
 * @param {Object} flowData - Данные потока
 * @param {Array} messages - Сообщения потока
 */
async function createFlowWithMessages(flowData, messages) {
    try {
        // Создаем поток
        const flow = await Flow.create({
            id: uuidv4(),
            ...flowData
        });

        // Создаем сообщения для потока
        for (const message of messages) {
            await FlowMessage.create({
                id: uuidv4(),
                flowId: flow.id,
                ...message
            });
        }

        logger.info(`Created flow: ${flow.name} with ${messages.length} messages`);
        return flow;
    } catch (error) {
        logger.error(`Error creating flow ${flowData.name}: ${error.message}`);
        throw error;
    }
}

/**
 * Заполняет базу данных тестовыми потоками
 */
async function seedFlows() {
    try {
        logger.info('Starting flow seeding...');

        // 1. Оформление СНИЛС
        await createFlowWithMessages(
            {
                name: 'Оформление СНИЛС',
                description: 'Оформление страхового номера индивидуального лицевого счёта',
                aiPrompt: 'Проанализируй диалог. Для оформления СНИЛС необходимо: паспорт, заявление. Если все документы в наличии, напиши "pass", иначе укажи какие документы отсутствуют.',
                successMessage: 'Отлично! У вас есть все необходимые документы. Пожалуйста, пройдите к окну №5 для оформления СНИЛС.',
                failureMessage: 'К сожалению, у вас отсутствуют необходимые документы для оформления СНИЛС. Пожалуйста, подготовьте паспорт и заполненное заявление, после чего вы можете вернуться для получения услуги.'
            },
            [
                {
                    content: 'Добро пожаловать в сервис оформления СНИЛС. Для начала процедуры мне нужно задать вам несколько вопросов. У вас есть при себе паспорт? {да|нет}',
                    step: 0
                },
                {
                    content: 'Вы уже заполнили заявление на оформление СНИЛС? {да|нет}',
                    step: 1
                },
                {
                    content: 'Вы оформляете СНИЛС впервые или хотите получить дубликат? Ответьте "впервые" или "дубликат".',
                    step: 2
                },
                {
                    content: 'Укажите вашу дату рождения в формате ДД.ММ.ГГГГ. {date}',
                    step: 3
                }
            ]
        );

        // 2. Государственная регистрация расторжения брака
        await createFlowWithMessages(
            {
                name: 'Расторжение брака',
                description: 'Государственная регистрация расторжения брака через МФЦ',
                aiPrompt: 'Проанализируй диалог. Для расторжения брака необходимы: паспорт, свидетельство о браке, решение суда о расторжении брака, квитанция об оплате госпошлины. Возраст должен быть старше 18 лет. Если все документы в наличии и возраст соответствует, напиши "pass", иначе укажи какие требования не соблюдены.',
                successMessage: 'Отлично! У вас есть все необходимые документы и вы соответствуете требованиям. Пожалуйста, пройдите к окну №8 для регистрации расторжения брака.',
                failureMessage: 'К сожалению, вы не соответствуете требованиям для регистрации расторжения брака. Пожалуйста, убедитесь, что вам исполнилось 18 лет и у вас есть все необходимые документы: паспорт, свидетельство о браке, решение суда и квитанция об оплате госпошлины.'
            },
            [
                {
                    content: 'Добро пожаловать в сервис регистрации расторжения брака. Для начала процедуры мне нужно задать вам несколько вопросов. У вас есть при себе паспорт? {да|нет}',
                    step: 0
                },
                {
                    content: 'У вас есть свидетельство о заключении брака? {да|нет}',
                    step: 1
                },
                {
                    content: 'У вас есть решение суда о расторжении брака, вступившее в законную силу? {да|нет}',
                    step: 2
                },
                {
                    content: 'Вы оплатили государственную пошлину за регистрацию расторжения брака? {да|нет}',
                    step: 3
                },
                {
                    content: 'Укажите ваш возраст (полных лет). {numeric}',
                    step: 4
                }
            ]
        );

        // 3. Выдача справки о наличии (отсутствии) судимости
        await createFlowWithMessages(
            {
                name: 'Справка о судимости',
                description: 'Выдача справки о наличии (отсутствии) судимости',
                aiPrompt: 'Проанализируй диалог. Для получения справки о судимости необходимы: паспорт, заявление, согласие на обработку персональных данных. Возраст должен быть старше 14 лет. Если все документы в наличии и возраст соответствует, напиши "pass", иначе укажи какие требования не соблюдены.',
                successMessage: 'Отлично! У вас есть все необходимые документы и вы соответствуете требованиям. Пожалуйста, пройдите к окну №3 для получения справки о судимости.',
                failureMessage: 'К сожалению, вы не соответствуете требованиям для получения справки о судимости. Пожалуйста, убедитесь, что вам исполнилось 14 лет и у вас есть все необходимые документы: паспорт, заявление и согласие на обработку персональных данных.'
            },
            [
                {
                    content: 'Добро пожаловать в сервис получения справки о наличии (отсутствии) судимости. Для начала процедуры мне нужно задать вам несколько вопросов. У вас есть при себе паспорт или иной документ, удостоверяющий личность? {да|нет}',
                    step: 0
                },
                {
                    content: 'Вы заполнили заявление на получение справки о наличии (отсутствии) судимости? {да|нет}',
                    step: 1
                },
                {
                    content: 'Вы подписали согласие на обработку персональных данных? {да|нет}',
                    step: 2
                },
                {
                    content: 'Укажите ваш возраст (полных лет). {numeric}',
                    step: 3
                },
                {
                    content: 'Вы получаете справку для себя или по доверенности для другого лица? Ответьте "для себя" или "по доверенности".',
                    step: 4
                },
                {
                    content: 'Если по доверенности, есть ли у вас нотариально заверенная доверенность? {да|нет}',
                    step: 5
                }
            ]
        );

        // 4. Age verification flow (not a starting flow)
        await createFlowWithMessages(
            {
                name: 'Возрастное ограничение',
                description: 'Поток для пользователей младше 18 лет',
                isStartingFlow: false,
                successMessage: 'К сожалению, данная услуга доступна только для лиц, достигших 18 лет. Пожалуйста, обратитесь к нам после достижения совершеннолетия.',
                failureMessage: 'К сожалению, данная услуга доступна только для лиц, достигших 18 лет.'
            },
            [
                {
                    content: 'К сожалению, данная услуга доступна только для лиц, достигших 18 лет. Вы можете обратиться к нам после достижения совершеннолетия. Есть ли у вас другие вопросы, с которыми мы можем помочь?',
                    step: 0
                }
            ]
        );

        // 5. Enhanced СНИЛС flow with age verification
        await createFlowWithMessages(
            {
                name: 'Оформление СНИЛС с проверкой возраста',
                description: 'Оформление СНИЛС с автоматической проверкой возраста',
                aiPrompt: 'Проанализируй диалог. Для оформления СНИЛС необходимо: паспорт, заявление, возраст 18+. Если все документы в наличии и возраст соответствует, напиши "pass", иначе укажи какие требования не соблюдены.',
                successMessage: 'Отлично! У вас есть все необходимые документы и вы соответствуете требованиям. Пожалуйста, пройдите к окну №5 для оформления СНИЛС.',
                failureMessage: 'К сожалению, у вас отсутствуют необходимые документы для оформления СНИЛС.'
            },
            [
                {
                    content: 'Добро пожаловать в сервис оформления СНИЛС. Для начала процедуры мне нужно задать вам несколько вопросов. У вас есть при себе паспорт? {да|нет}',
                    step: 0
                },
                {
                    content: 'Укажите ваш возраст (полных лет). {numeric}',
                    step: 1
                },
                {
                    content: 'Проверяем ваш возраст...',
                    step: 2,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'age_check',
                            operator: '<',
                            value: 18
                        },
                        successFlowId: null, // Will be set to age restriction flow ID (if under 18)
                        failFlowId: 'continue' // Continue current flow if 18 or older
                    }
                },
                {
                    content: 'Вы оформляете СНИЛС впервые или хотите получить дубликат? Ответьте "впервые" или "дубликат".',
                    step: 3
                }
            ]
        );

        // 6. Yes/No branching example flow
        await createFlowWithMessages(
            {
                name: 'Консультация по документам',
                description: 'Поток с ветвлением на основе ответов да/нет',
                successMessage: 'Спасибо за обращение! Мы предоставили вам всю необходимую информацию.',
                failureMessage: 'Если у вас остались вопросы, обратитесь к нашим специалистам.'
            },
            [
                {
                    content: 'Добро пожаловать в службу консультаций! Вы впервые обращаетесь в наш центр? {да|нет}',
                    step: 0
                },
                {
                    content: 'Проверяем ваш статус...',
                    step: 1,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'да'
                        },
                        targetFlowId: 'continue'
                    }
                },
                {
                    content: 'Отлично! Для новых клиентов у нас есть специальная программа. Хотели бы вы узнать о наших услугах подробнее? {да|нет}',
                    step: 2
                },
                {
                    content: 'Спасибо за ваши ответы! Наш специалист свяжется с вами в ближайшее время.',
                    step: 3
                }
            ]
        );

        logger.info('Flow seeding completed successfully');
    } catch (error) {
        logger.error(`Flow seeding failed: ${error.message}`);
        throw error;
    }
}

export default seedFlows;