import {v4 as uuidv4} from 'uuid';
import {Flow, FlowMessage} from '../models/index.js';
import logger from '../utils/logger.js';

/**
 * Создает поток и его сообщения
 * @param {Object} flowData - Данные потока
 * @param {Array} messages - Сообщения потока
 */
async function createFlowWithMessages(flowData, messages) {
    try {
        // Создаем поток
        const flow = await Flow.create({
            id: uuidv4(),
            ...flowData
        });

        // Создаем сообщения для потока
        for (const message of messages) {
            await FlowMessage.create({
                id: uuidv4(),
                flowId: flow.id,
                ...message
            });
        }

        logger.info(`Created flow: ${flow.name} with ${messages.length} messages`);
        return flow;
    } catch (error) {
        logger.error(`Error creating flow ${flowData.name}: ${error.message}`);
        throw error;
    }
}

/**
 * Заполняет базу данных продвинутыми потоками с новой системой навигации
 */
async function seedAdvancedFlows() {
    try {
        logger.info('Starting advanced flow seeding...');

        // 1. Поток с внутренними переходами и циклами
        await createFlowWithMessages(
            {
                name: 'Анкета с проверками',
                description: 'Демонстрация внутренних переходов и циклов',
                isStartingFlow: true,
                aiPrompt: 'Проанализируй анкету. Все поля должны быть заполнены корректно.',
                successMessage: 'Анкета заполнена успешно!',
                failureMessage: 'Анкета заполнена некорректно.'
            },
            [
                {
                    content: 'Добро пожаловать! Заполним анкету. Как вас зовут?',
                    step: 0
                },
                {
                    content: 'Укажите ваш возраст (полных лет). {numeric}',
                    step: 1
                },
                {
                    content: 'Проверяем возраст...',
                    step: 2,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'age_check',
                            operator: '<',
                            value: 18
                        },
                        successAction: { goto: '#10' }, // Переход к сообщению о возрастном ограничении
                        failAction: { goto: 'continue' } // Продолжить если 18+
                    }
                },
                {
                    content: 'У вас есть паспорт? {да|нет}',
                    step: 3
                },
                {
                    content: 'Проверяем документы...',
                    step: 4,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'нет'
                        },
                        successAction: { goto: '#8' }, // Переход к запросу документов
                        failAction: { goto: 'continue' } // Продолжить если есть паспорт
                    }
                },
                {
                    content: 'Отлично! Укажите номер паспорта.',
                    step: 5
                },
                {
                    content: 'Хотите добавить дополнительную информацию? {да|нет}',
                    step: 6
                },
                {
                    content: 'Переход к завершению...',
                    step: 7,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'always_true'
                        },
                        successAction: { goto: '#12' }, // Безусловный переход к завершению
                        failAction: { goto: 'continue' } // Никогда не выполнится
                    }
                },
                {
                    content: 'Пожалуйста, подготовьте паспорт и вернитесь. Хотите попробовать снова? {да|нет}',
                    step: 8
                },
                {
                    content: 'Проверяем желание повторить...',
                    step: 9,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'да'
                        },
                        successAction: { goto: '#3' }, // Возврат к вопросу о паспорте
                        failAction: { goto: '#12' } // Переход к завершению
                    }
                },
                {
                    content: 'К сожалению, данная услуга доступна только лицам старше 18 лет.',
                    step: 10
                },
                {
                    content: 'Обратитесь к нам после достижения совершеннолетия.',
                    step: 11
                },
                {
                    content: 'Спасибо за заполнение анкеты!',
                    step: 12
                }
            ]
        );

        // 2. Поток с переходами между потоками
        const helperFlow = await createFlowWithMessages(
            {
                name: 'Помощник по документам',
                description: 'Вспомогательный поток для консультации',
                isStartingFlow: false,
                successMessage: 'Консультация завершена.',
                failureMessage: 'Обратитесь за дополнительной помощью.'
            },
            [
                {
                    content: 'Вы попали в раздел помощи. Какой документ вас интересует?',
                    step: 0
                },
                {
                    content: 'Для паспорта нужны: заявление, фото, старый паспорт (если есть).',
                    step: 1
                },
                {
                    content: 'Хотите вернуться к основному процессу? {да|нет}',
                    step: 2
                }
            ]
        );

        await createFlowWithMessages(
            {
                name: 'Основной процесс с переходами',
                description: 'Демонстрация переходов между потоками',
                isStartingFlow: true,
                aiPrompt: 'Проверь, что пользователь получил всю необходимую информацию.',
                successMessage: 'Процесс завершен успешно!',
                failureMessage: 'Процесс не завершен.'
            },
            [
                {
                    content: 'Добро пожаловать! Нужна ли вам помощь с документами? {да|нет}',
                    step: 0
                },
                {
                    content: 'Направляем в раздел помощи...',
                    step: 1,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'да'
                        },
                        successAction: { goto: helperFlow.id }, // Переход к другому потоку
                        failAction: { goto: 'continue' } // Продолжить текущий поток
                    }
                },
                {
                    content: 'Отлично! Тогда продолжим с основным процессом.',
                    step: 2
                },
                {
                    content: 'Хотите перейти к конкретному шагу помощи? {да|нет}',
                    step: 3
                },
                {
                    content: 'Переходим к конкретному шагу...',
                    step: 4,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'да'
                        },
                        successAction: { goto: `${helperFlow.id}#1` }, // Переход к конкретному шагу в другом потоке
                        failAction: { goto: 'continue' }
                    }
                },
                {
                    content: 'Процесс завершен. Спасибо!',
                    step: 5
                }
            ]
        );

        // 3. Поток с комплексной логикой ветвления
        await createFlowWithMessages(
            {
                name: 'Комплексное ветвление',
                description: 'Демонстрация сложной логики переходов',
                isStartingFlow: true,
                aiPrompt: 'Проверь корректность прохождения всех этапов.',
                successMessage: 'Все этапы пройдены!',
                failureMessage: 'Не все этапы завершены.'
            },
            [
                {
                    content: 'Выберите тип услуги: "паспорт", "справка" или "консультация".',
                    step: 0
                },
                {
                    content: 'Обрабатываем выбор услуги...',
                    step: 1,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'contains',
                            value: 'паспорт'
                        },
                        successAction: { goto: '#10' }, // Ветка паспорта
                        failAction: { goto: '#2' } // Проверить другие варианты
                    }
                },
                {
                    content: 'Проверяем справку...',
                    step: 2,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'contains',
                            value: 'справка'
                        },
                        successAction: { goto: '#15' }, // Ветка справки
                        failAction: { goto: '#20' } // Ветка консультации
                    }
                },
                // Ветка паспорта (шаги 10-14)
                {
                    content: 'Вы выбрали оформление паспорта. Есть ли у вас старый паспорт? {да|нет}',
                    step: 10
                },
                {
                    content: 'Отлично! Это замена паспорта.',
                    step: 11
                },
                {
                    content: 'Переход к общему завершению...',
                    step: 12,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'always_true'
                        },
                        successAction: { goto: '#25' }, // Общее завершение
                        failAction: { goto: 'continue' }
                    }
                },
                // Ветка справки (шаги 15-19)
                {
                    content: 'Вы выбрали получение справки. Какая справка нужна?',
                    step: 15
                },
                {
                    content: 'Справка будет готова в течение 5 дней.',
                    step: 16
                },
                {
                    content: 'Переход к завершению...',
                    step: 17,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'always_true'
                        },
                        successAction: { goto: '#25' },
                        failAction: { goto: 'continue' }
                    }
                },
                // Ветка консультации (шаги 20-24)
                {
                    content: 'Вы выбрали консультацию. По какому вопросу?',
                    step: 20
                },
                {
                    content: 'Наш специалист ответит на ваши вопросы.',
                    step: 21
                },
                {
                    content: 'Нужна ли дополнительная помощь? {да|нет}',
                    step: 22
                },
                {
                    content: 'Проверяем потребность в помощи...',
                    step: 23,
                    messageType: 'conditional_switch',
                    conditionConfig: {
                        condition: {
                            type: 'yes_no',
                            value: 'да'
                        },
                        successAction: { goto: '#20' }, // Возврат к началу консультации
                        failAction: { goto: '#25' } // Переход к завершению
                    }
                },
                // Общее завершение (шаг 25)
                {
                    content: 'Спасибо за обращение! Ваш запрос обработан.',
                    step: 25
                }
            ]
        );

        logger.info('Advanced flow seeding completed successfully');
    } catch (error) {
        logger.error(`Advanced flow seeding failed: ${error.message}`);
        throw error;
    }
}

export default seedAdvancedFlows;
