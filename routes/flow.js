import express from 'express';
import {Flow, FlowMessage} from '../models/index.js';
import {v4 as uuidv4} from 'uuid';
import logger from '../utils/logger.js';
import {
    validateIsStartingFlow,
    validateFlowMessageData
} from '../utils/flowValidation.js';

const router = express.Router();

/**
 * @route POST /api/flow
 * @description Create a new flow
 * @access Public
 */
router.post('/', async (req, res) => {
    try {
        const {name, description, aiPrompt, successMessage, failureMessage} = req.body;

        if (!name) {
            return res.status(400).json({error: 'Name is required'});
        }

        const flow = await Flow.create({
            id: uuidv4(),
            name,
            description: description || null,
            aiPrompt: aiPrompt || null,
            successMessage: successMessage || null,
            failureMessage: failureMessage || null
        });

        logger.info(`Created new flow with ID: ${flow.id}`);
        return res.status(201).json({
            flowId: flow.id,
            message: 'Flow created successfully'
        });
    } catch (error) {
        logger.error(`Error creating flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to create flow'});
    }
});

/**
 * @route GET /api/flow/:flowId
 * @description Get a flow by ID with its messages
 * @access Public
 */
router.get('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const flow = await Flow.findByPk(flowId, {
            include: [{
                model: FlowMessage,
                order: [['step', 'ASC']]
            }]
        });

        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        return res.status(200).json(flow);
    } catch (error) {
        logger.error(`Error getting flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to get flow'});
    }
});

/**
 * @route DELETE /api/flow/:flowId
 * @description Delete a flow
 * @access Public
 */
router.delete('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const flow = await Flow.findByPk(flowId);

        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        await flow.destroy();
        logger.info(`Deleted flow: ${flowId}`);

        return res.status(200).json({
            message: 'Flow deleted successfully'
        });
    } catch (error) {
        logger.error(`Error deleting flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to delete flow'});
    }
});

/**
 * @route POST /api/flow/:flowId/message
 * @description Add a message to a flow
 * @access Public
 */
router.post('/:flowId/message', async (req, res) => {
    try {
        const {flowId} = req.params;
        const {content, step} = req.body;

        if (!content || step === undefined) {
            return res.status(400).json({error: 'Content and step are required'});
        }

        const flow = await Flow.findByPk(flowId);
        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        // Check if a message with this step already exists
        const existingMessage = await FlowMessage.findOne({
            where: {flowId, step}
        });

        if (existingMessage) {
            return res.status(400).json({
                error: `A message with step ${step} already exists in this flow`
            });
        }

        const message = await FlowMessage.create({
            id: uuidv4(),
            flowId,
            content,
            step
        });

        logger.info(`Added message to flow ${flowId} at step ${step}`);
        return res.status(201).json(message);
    } catch (error) {
        logger.error(`Error adding message to flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to add message to flow'});
    }
});

/**
 * @route PUT /api/flow/:flowId/message/:messageId
 * @description Update a message in a flow
 * @access Public
 */
router.put('/:flowId/message/:messageId', async (req, res) => {
    try {
        const {flowId, messageId} = req.params;
        const {content, step} = req.body;

        if (!content && step === undefined) {
            return res.status(400).json({error: 'At least one field (content or step) is required'});
        }

        const message = await FlowMessage.findOne({
            where: {id: messageId, flowId}
        });

        if (!message) {
            return res.status(404).json({error: 'Message not found in this flow'});
        }

        // If changing step, check if the new step is already taken
        if (step !== undefined && step !== message.step) {
            const existingMessage = await FlowMessage.findOne({
                where: {flowId, step}
            });

            if (existingMessage) {
                return res.status(400).json({
                    error: `A message with step ${step} already exists in this flow`
                });
            }
        }

        const updateData = {};
        if (content) updateData.content = content;
        if (step !== undefined) updateData.step = step;

        await message.update(updateData);
        logger.info(`Updated message ${messageId} in flow ${flowId}`);

        return res.status(200).json(await message.reload());
    } catch (error) {
        logger.error(`Error updating message: ${error.message}`);
        return res.status(500).json({error: 'Failed to update message'});
    }
});

/**
 * @route DELETE /api/flow/:flowId/message/:messageId
 * @description Delete a message from a flow
 * @access Public
 */
router.delete('/:flowId/message/:messageId', async (req, res) => {
    try {
        const {flowId, messageId} = req.params;

        const message = await FlowMessage.findOne({
            where: {id: messageId, flowId}
        });

        if (!message) {
            return res.status(404).json({error: 'Message not found in this flow'});
        }

        await message.destroy();
        logger.info(`Deleted message ${messageId} from flow ${flowId}`);

        return res.status(200).json({
            message: 'Message deleted successfully'
        });
    } catch (error) {
        logger.error(`Error deleting message: ${error.message}`);
        return res.status(500).json({error: 'Failed to delete message'});
    }
});

/**
 * @route GET /api/flow
 * @description Get all flows
 * @access Public
 */
router.get('/', async (req, res) => {
    try {
        const flows = await Flow.findAll({
            order: [['createdAt', 'DESC']]
        });

        return res.status(200).json(flows);
    } catch (error) {
        logger.error(`Error getting flows: ${error.message}`);
        return res.status(500).json({error: 'Failed to get flows'});
    }
});

/**
 * @route PUT /api/flow/:flowId
 * @description Update a flow
 * @access Public
 */
router.put('/:flowId', async (req, res) => {
    try {
        const {flowId} = req.params;
        const {name, description, aiPrompt, successMessage, failureMessage} = req.body;

        const flow = await Flow.findByPk(flowId);
        if (!flow) {
            return res.status(404).json({error: 'Flow not found'});
        }

        const updateData = {};
        if (name) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (aiPrompt !== undefined) updateData.aiPrompt = aiPrompt;
        if (successMessage !== undefined) updateData.successMessage = successMessage;
        if (failureMessage !== undefined) updateData.failureMessage = failureMessage;

        await flow.update(updateData);
        logger.info(`Updated flow ${flowId}`);

        return res.status(200).json(await flow.reload());
    } catch (error) {
        logger.error(`Error updating flow: ${error.message}`);
        return res.status(500).json({error: 'Failed to update flow'});
    }
});

export default router;