import { expect } from 'chai';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import app from '../app.js'; // Assuming you have an app.js file
import { Flow, FlowMessage } from '../models/index.js';
import { sequelize } from '../db.js';

describe('Flow API Routes with Conditional Switching', function() {
    let testFlow1, testFlow2;

    before(async function() {
        // Sync database
        await sequelize.sync({ force: true });
    });

    beforeEach(async function() {
        // Clean up before each test
        await FlowMessage.destroy({ where: {} });
        await Flow.destroy({ where: {} });

        // Create test flows
        testFlow1 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 1',
            description: 'Test flow for API testing',
            isStartingFlow: true
        });

        testFlow2 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 2',
            description: 'Secondary test flow',
            isStartingFlow: false
        });
    });

    afterEach(async function() {
        // Clean up after each test
        await FlowMessage.destroy({ where: {} });
        await Flow.destroy({ where: {} });
    });

    describe('POST /api/flow - Create Flow', function() {
        it('should create a flow with isStartingFlow parameter', async function() {
            const flowData = {
                name: 'New Test Flow',
                description: 'A new test flow',
                isStartingFlow: false
            };

            const response = await request(app)
                .post('/api/flow')
                .send(flowData)
                .expect(201);

            expect(response.body).to.have.property('flowId');
            expect(response.body.isStartingFlow).to.equal(false);
            expect(response.body.name).to.equal(flowData.name);
        });

        it('should default isStartingFlow to true if not provided', async function() {
            const flowData = {
                name: 'Default Starting Flow'
            };

            const response = await request(app)
                .post('/api/flow')
                .send(flowData)
                .expect(201);

            expect(response.body.isStartingFlow).to.equal(true);
        });

        it('should reject invalid isStartingFlow values', async function() {
            const flowData = {
                name: 'Invalid Flow',
                isStartingFlow: 'not-a-boolean'
            };

            const response = await request(app)
                .post('/api/flow')
                .send(flowData)
                .expect(400);

            expect(response.body.error).to.include('boolean');
        });
    });

    describe('PUT /api/flow/:flowId - Update Flow', function() {
        it('should update isStartingFlow field', async function() {
            const updateData = {
                isStartingFlow: false
            };

            const response = await request(app)
                .put(`/api/flow/${testFlow1.id}`)
                .send(updateData)
                .expect(200);

            expect(response.body.isStartingFlow).to.equal(false);
        });

        it('should reject invalid isStartingFlow values in updates', async function() {
            const updateData = {
                isStartingFlow: 'invalid'
            };

            await request(app)
                .put(`/api/flow/${testFlow1.id}`)
                .send(updateData)
                .expect(400);
        });
    });

    describe('POST /api/flow/:flowId/message - Create Flow Message', function() {
        it('should create a regular message', async function() {
            const messageData = {
                content: 'What is your name?',
                step: 0,
                messageType: 'regular'
            };

            const response = await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(201);

            expect(response.body.messageType).to.equal('regular');
            expect(response.body.conditionConfig).to.be.null;
        });

        it('should create a conditional_switch message with valid config', async function() {
            const messageData = {
                content: 'Checking your age...',
                step: 1,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'age_check',
                        stepReference: 0,
                        operator: '<',
                        value: 18
                    },
                    trueFlowId: testFlow2.id,
                    falseFlowId: null,
                    trueMessage: 'You are under 18',
                    falseMessage: 'You meet the age requirement'
                }
            };

            const response = await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(201);

            expect(response.body.messageType).to.equal('conditional_switch');
            expect(response.body.conditionConfig).to.be.an('object');
            expect(response.body.conditionConfig.condition.type).to.equal('age_check');
        });

        it('should reject conditional_switch message without conditionConfig', async function() {
            const messageData = {
                content: 'Invalid conditional message',
                step: 1,
                messageType: 'conditional_switch'
            };

            const response = await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(400);

            expect(response.body.error).to.include('conditionConfig is required');
        });

        it('should reject regular message with conditionConfig', async function() {
            const messageData = {
                content: 'Regular message with config',
                step: 1,
                messageType: 'regular',
                conditionConfig: {
                    condition: { type: 'yes_no', value: 'да' }
                }
            };

            const response = await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(400);

            expect(response.body.error).to.include('conditionConfig can only be provided for conditional_switch');
        });

        it('should reject invalid condition types', async function() {
            const messageData = {
                content: 'Invalid condition',
                step: 1,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'invalid_type',
                        value: 'test'
                    }
                }
            };

            await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(400);
        });

        it('should reject references to non-existent flows', async function() {
            const messageData = {
                content: 'Invalid flow reference',
                step: 1,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'yes_no',
                        stepReference: 0,
                        value: 'да'
                    },
                    trueFlowId: uuidv4(), // Non-existent flow
                    falseFlowId: null
                }
            };

            const response = await request(app)
                .post(`/api/flow/${testFlow1.id}/message`)
                .send(messageData)
                .expect(400);

            expect(response.body.error).to.include('does not exist');
        });
    });

    describe('PUT /api/flow/:flowId/message/:messageId - Update Flow Message', function() {
        let testMessage;

        beforeEach(async function() {
            testMessage = await FlowMessage.create({
                id: uuidv4(),
                flowId: testFlow1.id,
                content: 'Original message',
                step: 0,
                messageType: 'regular'
            });
        });

        it('should update message to conditional_switch type', async function() {
            const updateData = {
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'yes_no',
                        stepReference: 0,
                        value: 'да'
                    },
                    trueFlowId: testFlow2.id,
                    falseFlowId: null
                }
            };

            const response = await request(app)
                .put(`/api/flow/${testFlow1.id}/message/${testMessage.id}`)
                .send(updateData)
                .expect(200);

            expect(response.body.messageType).to.equal('conditional_switch');
            expect(response.body.conditionConfig).to.be.an('object');
        });

        it('should clear conditionConfig when changing to regular type', async function() {
            // First create a conditional message
            await testMessage.update({
                messageType: 'conditional_switch',
                conditionConfig: { condition: { type: 'yes_no', value: 'да' } }
            });

            const updateData = {
                messageType: 'regular'
            };

            const response = await request(app)
                .put(`/api/flow/${testFlow1.id}/message/${testMessage.id}`)
                .send(updateData)
                .expect(200);

            expect(response.body.messageType).to.equal('regular');
            expect(response.body.conditionConfig).to.be.null;
        });
    });

    describe('GET /api/flow/starting-flows - Get Starting Flows', function() {
        it('should return only flows marked as starting flows', async function() {
            const response = await request(app)
                .get('/api/flow/starting-flows')
                .expect(200);

            expect(response.body).to.be.an('array');
            expect(response.body).to.have.length(1);
            expect(response.body[0].id).to.equal(testFlow1.id);
            expect(response.body[0].isStartingFlow).to.be.true;
        });
    });

    describe('POST /api/flow/validate-condition - Validate Condition', function() {
        it('should validate a correct condition configuration', async function() {
            const conditionConfig = {
                condition: {
                    type: 'age_check',
                    stepReference: 0,
                    operator: '>=',
                    value: 18
                },
                trueFlowId: testFlow2.id,
                falseFlowId: null
            };

            const response = await request(app)
                .post('/api/flow/validate-condition')
                .send({ conditionConfig })
                .expect(200);

            expect(response.body.valid).to.be.true;
        });

        it('should reject invalid condition configuration', async function() {
            const conditionConfig = {
                condition: {
                    type: 'invalid_type',
                    value: 'test'
                }
            };

            const response = await request(app)
                .post('/api/flow/validate-condition')
                .send({ conditionConfig })
                .expect(400);

            expect(response.body.valid).to.be.false;
            expect(response.body.error).to.be.a('string');
        });
    });
});
