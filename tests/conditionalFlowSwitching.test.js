import { expect } from 'chai';
import { v4 as uuidv4 } from 'uuid';
import { Dialog, Flow, FlowMessage, Message } from '../models/index.js';
import flowDialogService from '../services/flowDialogService.js';
import conditionEvaluator from '../services/conditionEvaluator.js';
import { sequelize } from '../db.js';

describe('Conditional Flow Switching', function() {
    let testFlow1, testFlow2, testDialog;

    before(async function() {
        // Sync database
        await sequelize.sync({ force: true });
    });

    beforeEach(async function() {
        // Create test flows
        testFlow1 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 1',
            description: 'Main test flow',
            isStartingFlow: true
        });

        testFlow2 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 2',
            description: 'Secondary test flow',
            isStartingFlow: false
        });

        // Create test flow messages for flow 1
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'What is your age? {numeric}',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'Checking age requirements...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    stepReference: 0,
                    operator: '<',
                    value: 18
                },
                trueFlowId: testFlow2.id,
                falseFlowId: null,
                trueMessage: 'You are under 18, switching to appropriate flow.',
                falseMessage: 'You meet the age requirement. Continuing...'
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'Thank you for providing your information.',
            step: 2
        });

        // Create test flow messages for flow 2
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow2.id,
            content: 'This service is only available for users 18 and older.',
            step: 0
        });

        // Create test dialog
        testDialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            currentStep: 0
        });
    });

    afterEach(async function() {
        // Clean up test data
        await Message.destroy({ where: {} });
        await FlowMessage.destroy({ where: {} });
        await Dialog.destroy({ where: {} });
        await Flow.destroy({ where: {} });
    });

    describe('Condition Evaluator', function() {
        it('should correctly evaluate age check conditions', function() {
            const condition = {
                type: 'age_check',
                operator: '<',
                value: 18
            };

            const result1 = conditionEvaluator.evaluateCondition(condition, '16', {}, 0);
            const result2 = conditionEvaluator.evaluateCondition(condition, '20', {}, 0);

            expect(result1).to.be.true;
            expect(result2).to.be.false;
        });

        it('should correctly evaluate yes/no conditions', function() {
            const condition = {
                type: 'yes_no',
                value: 'да'
            };

            const result1 = conditionEvaluator.evaluateCondition(condition, 'да', {}, 0);
            const result2 = conditionEvaluator.evaluateCondition(condition, 'нет', {}, 0);

            expect(result1).to.be.true;
            expect(result2).to.be.false;
        });

        it('should correctly evaluate step reference conditions', function() {
            const condition = {
                type: 'equals',
                stepReference: 0,
                value: 'test_value'
            };

            const collectedData = {
                step_0: {
                    processed: 'test_value'
                }
            };

            const result = conditionEvaluator.evaluateCondition(condition, 'current', collectedData, 1);
            expect(result).to.be.true;
        });
    });

    describe('Flow Switching Logic', function() {
        it('should switch to different flow when condition is met', async function() {
            // Start dialog with first message
            await flowDialogService.startDialog(testFlow1.id, testDialog);

            // Process user response with age under 18
            const result = await flowDialogService.processMessage(testDialog.id, '16');

            // Verify flow was switched
            expect(result.flowSwitched).to.be.true;
            expect(result.newFlowId).to.equal(testFlow2.id);
            expect(result.message).to.include('You are under 18');

            // Verify dialog was updated
            const updatedDialog = await Dialog.findByPk(testDialog.id);
            expect(updatedDialog.flowId).to.equal(testFlow2.id);
            expect(updatedDialog.currentStep).to.equal(0);
        });

        it('should continue with current flow when condition is not met', async function() {
            // Start dialog with first message
            await flowDialogService.startDialog(testFlow1.id, testDialog);

            // Process user response with age 18 or over
            const result = await flowDialogService.processMessage(testDialog.id, '20');

            // Verify flow was not switched
            expect(result.flowSwitched).to.be.undefined;
            expect(result.message).to.include('You meet the age requirement');

            // Verify dialog stayed in same flow
            const updatedDialog = await Dialog.findByPk(testDialog.id);
            expect(updatedDialog.flowId).to.equal(testFlow1.id);
            expect(updatedDialog.currentStep).to.equal(2);
        });

        it('should handle missing target flow gracefully', async function() {
            // Update condition config to reference non-existent flow
            const conditionalMessage = await FlowMessage.findOne({
                where: {
                    flowId: testFlow1.id,
                    step: 1,
                    messageType: 'conditional_switch'
                }
            });

            await conditionalMessage.update({
                conditionConfig: {
                    ...conditionalMessage.conditionConfig,
                    trueFlowId: uuidv4() // Non-existent flow ID
                }
            });

            // Start dialog
            await flowDialogService.startDialog(testFlow1.id, testDialog);

            // Process message that should trigger switch
            try {
                await flowDialogService.processMessage(testDialog.id, '16');
                expect.fail('Should have thrown an error for missing target flow');
            } catch (error) {
                expect(error.message).to.include('not found');
            }
        });
    });

    describe('Multiple Conditions', function() {
        beforeEach(async function() {
            // Create a message with multiple conditions
            await FlowMessage.create({
                id: uuidv4(),
                flowId: testFlow1.id,
                content: 'Multiple condition test',
                step: 3,
                messageType: 'conditional_switch',
                conditionConfig: {
                    conditions: [
                        {
                            type: 'age_check',
                            stepReference: 0,
                            operator: '>=',
                            value: 18
                        },
                        {
                            type: 'yes_no',
                            field: 'current_response',
                            value: 'да'
                        }
                    ],
                    logic: 'AND',
                    trueFlowId: testFlow2.id,
                    falseFlowId: null,
                    trueMessage: 'Both conditions met',
                    falseMessage: 'Conditions not met'
                }
            });
        });

        it('should evaluate multiple conditions with AND logic', function() {
            const conditions = [
                { type: 'age_check', operator: '>=', value: 18 },
                { type: 'yes_no', value: 'да' }
            ];

            const collectedData = {};
            
            const result1 = conditionEvaluator.evaluateMultipleConditions(
                conditions, 'AND', 'да', collectedData, 0
            );
            
            const result2 = conditionEvaluator.evaluateMultipleConditions(
                conditions, 'AND', 'нет', collectedData, 0
            );

            expect(result1).to.be.false; // Age not provided in collectedData
            expect(result2).to.be.false;
        });

        it('should evaluate multiple conditions with OR logic', function() {
            const conditions = [
                { type: 'yes_no', value: 'да' },
                { type: 'yes_no', value: 'нет' }
            ];

            const result1 = conditionEvaluator.evaluateMultipleConditions(
                conditions, 'OR', 'да', {}, 0
            );
            
            const result2 = conditionEvaluator.evaluateMultipleConditions(
                conditions, 'OR', 'maybe', {}, 0
            );

            expect(result1).to.be.true;
            expect(result2).to.be.false;
        });
    });
});
