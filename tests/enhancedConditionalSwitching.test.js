import { expect } from 'chai';
import { v4 as uuidv4 } from 'uuid';
import { Dialog, Flow, FlowMessage, Message } from '../models/index.js';
import flowDialogService from '../services/flowDialogService.js';
import { sequelize } from '../db.js';

describe('Enhanced Conditional Flow Switching', function() {
    let testFlow, testDialog;

    before(async function() {
        // Sync database
        await sequelize.sync({ force: true });
    });

    beforeEach(async function() {
        // Create test flow with enhanced navigation
        testFlow = await Flow.create({
            id: uuidv4(),
            name: 'Enhanced Navigation Test Flow',
            description: 'Test flow for enhanced navigation features',
            isStartingFlow: true,
            aiPrompt: 'Test prompt',
            successMessage: 'Success!',
            failureMessage: 'Failed!'
        });

        // Create test messages with new navigation format
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'What is your age? {numeric}',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Checking age...',
            step: 1,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'age_check',
                    operator: '<',
                    value: 18
                },
                successAction: { goto: '#10' }, // Jump to step 10 if under 18
                failAction: { goto: 'continue' } // Continue if 18+
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'You are 18 or older. Do you have documents? {да|нет}',
            step: 2
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Checking documents...',
            step: 3,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'yes_no',
                    value: 'нет'
                },
                successAction: { goto: '#8' }, // Jump to step 8 if no documents
                failAction: { goto: 'continue' } // Continue if has documents
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Great! You have all required documents.',
            step: 4
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Return to main flow...',
            step: 5,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'always_true'
                },
                successAction: { goto: '#12' }, // Unconditional jump to step 12
                failAction: { goto: 'continue' } // Never executed
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Please prepare your documents. Try again? {да|нет}',
            step: 8
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Checking retry...',
            step: 9,
            messageType: 'conditional_switch',
            conditionConfig: {
                condition: {
                    type: 'yes_no',
                    value: 'да'
                },
                successAction: { goto: '#2' }, // Return to document question
                failAction: { goto: '#12' } // Jump to end
            }
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'You are under 18. This service is not available.',
            step: 10
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Please return when you are 18.',
            step: 11
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow.id,
            content: 'Thank you for using our service!',
            step: 12
        });

        // Create test dialog
        testDialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });
    });

    afterEach(async function() {
        // Clean up test data
        await Message.destroy({ where: {} });
        await Dialog.destroy({ where: {} });
        await FlowMessage.destroy({ where: {} });
        await Flow.destroy({ where: {} });
    });

    describe('New Navigation Format', function() {
        it('should handle step jumping for under 18 users', async function() {
            // Start dialog
            await flowDialogService.startDialog(testFlow.id, testDialog);

            // User responds with age under 18
            const result = await flowDialogService.processMessage(testDialog.id, '16');

            // Should jump to step 10 (under 18 message)
            expect(result.step).to.equal(10);
            expect(result.message).to.include('under 18');
        });

        it('should continue flow for 18+ users', async function() {
            // Start dialog
            await flowDialogService.startDialog(testFlow.id, testDialog);

            // User responds with age 18+
            const result = await flowDialogService.processMessage(testDialog.id, '25');

            // Should continue to step 2
            expect(result.step).to.equal(2);
            expect(result.message).to.include('18 or older');
        });

        it('should handle document check and retry loop', async function() {
            // Start dialog and get to document question
            await flowDialogService.startDialog(testFlow.id, testDialog);
            await flowDialogService.processMessage(testDialog.id, '25'); // Age 25

            // User says no documents
            const noDocsResult = await flowDialogService.processMessage(testDialog.id, 'нет');
            expect(noDocsResult.step).to.equal(8);
            expect(noDocsResult.message).to.include('prepare your documents');

            // User wants to try again
            const retryResult = await flowDialogService.processMessage(testDialog.id, 'да');
            expect(retryResult.step).to.equal(2);
            expect(retryResult.message).to.include('Do you have documents');
        });

        it('should handle always_true condition for unconditional jumps', async function() {
            // Start dialog and get to step with always_true condition
            await flowDialogService.startDialog(testFlow.id, testDialog);
            await flowDialogService.processMessage(testDialog.id, '25'); // Age 25
            await flowDialogService.processMessage(testDialog.id, 'да'); // Has documents

            // Should reach step 4, then unconditionally jump to step 12
            const result = await flowDialogService.processMessage(testDialog.id, 'anything');
            expect(result.step).to.equal(12);
            expect(result.message).to.include('Thank you');
        });

        it('should maintain backward compatibility with old format', async function() {
            // Create a message with old format
            await FlowMessage.create({
                id: uuidv4(),
                flowId: testFlow.id,
                content: 'Old format test',
                step: 20,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'yes_no',
                        value: 'да'
                    },
                    successFlowId: 'continue',
                    failFlowId: 'continue'
                }
            });

            // Update dialog to step 20
            await testDialog.update({ currentStep: 20 });

            // Should still work with old format
            const result = await flowDialogService.processMessage(testDialog.id, 'да');
            expect(result.step).to.equal(21); // Should continue to next step
        });
    });

    describe('Error Handling', function() {
        it('should handle invalid goto values gracefully', async function() {
            // Create a message with invalid goto
            await FlowMessage.create({
                id: uuidv4(),
                flowId: testFlow.id,
                content: 'Invalid goto test',
                step: 30,
                messageType: 'conditional_switch',
                conditionConfig: {
                    condition: {
                        type: 'always_true'
                    },
                    successAction: { goto: '#invalid' },
                    failAction: { goto: 'continue' }
                }
            });

            // Update dialog to step 30
            await testDialog.update({ currentStep: 30 });

            try {
                await flowDialogService.processMessage(testDialog.id, 'test');
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).to.include('Invalid step number');
            }
        });

        it('should handle missing conditionConfig', async function() {
            // Create a conditional switch message without conditionConfig
            await FlowMessage.create({
                id: uuidv4(),
                flowId: testFlow.id,
                content: 'Missing config test',
                step: 40,
                messageType: 'conditional_switch'
                // No conditionConfig
            });

            // Update dialog to step 40
            await testDialog.update({ currentStep: 40 });

            try {
                await flowDialogService.processMessage(testDialog.id, 'test');
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).to.include('missing conditionConfig');
            }
        });
    });

    describe('Dialog State Management', function() {
        it('should update dialog state correctly during navigation', async function() {
            // Start dialog
            await flowDialogService.startDialog(testFlow.id, testDialog);

            // Process age (should jump to step 10)
            await flowDialogService.processMessage(testDialog.id, '16');

            // Verify dialog state
            await testDialog.reload();
            expect(testDialog.currentStep).to.equal(10);
            expect(testDialog.collectedData.lastProcessedResponse).to.equal('16');
        });

        it('should preserve collected data during navigation', async function() {
            // Start dialog
            await flowDialogService.startDialog(testFlow.id, testDialog);

            // Process multiple messages
            await flowDialogService.processMessage(testDialog.id, '25');
            await flowDialogService.processMessage(testDialog.id, 'нет');

            // Verify collected data
            await testDialog.reload();
            expect(testDialog.collectedData.step_0).to.exist;
            expect(testDialog.collectedData.step_0.processed).to.equal('25');
            expect(testDialog.collectedData.step_2).to.exist;
            expect(testDialog.collectedData.step_2.processed).to.equal('нет');
        });
    });
});
