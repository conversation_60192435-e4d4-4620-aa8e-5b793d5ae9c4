import { expect } from 'chai';
import { v4 as uuidv4 } from 'uuid';
import { Dialog, Flow, FlowMessage } from '../models/index.js';
import navigationService from '../services/navigationService.js';
import { sequelize } from '../db.js';

describe('Navigation Service', function() {
    let testFlow1, testFlow2, testDialog;

    before(async function() {
        // Sync database
        await sequelize.sync({ force: true });
    });

    beforeEach(async function() {
        // Create test flows
        testFlow1 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 1',
            description: 'First test flow',
            isStartingFlow: true
        });

        testFlow2 = await Flow.create({
            id: uuidv4(),
            name: 'Test Flow 2',
            description: 'Second test flow',
            isStartingFlow: false
        });

        // Create test messages for flow 1
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'Step 0 message',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'Step 1 message',
            step: 1
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            content: 'Step 5 message',
            step: 5
        });

        // Create test messages for flow 2
        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow2.id,
            content: 'Flow 2 Step 0 message',
            step: 0
        });

        await FlowMessage.create({
            id: uuidv4(),
            flowId: testFlow2.id,
            content: 'Flow 2 Step 3 message',
            step: 3
        });

        // Create test dialog
        testDialog = await Dialog.create({
            id: uuidv4(),
            flowId: testFlow1.id,
            currentStep: 0,
            completed: false,
            collectedData: {}
        });
    });

    afterEach(async function() {
        // Clean up test data
        await Dialog.destroy({ where: {} });
        await FlowMessage.destroy({ where: {} });
        await Flow.destroy({ where: {} });
    });

    describe('parseGoto', function() {
        it('should parse "continue" correctly', function() {
            const result = navigationService.parseGoto('continue');
            expect(result).to.deep.equal({
                type: 'continue',
                flowId: null,
                step: null
            });
        });

        it('should parse "#step" correctly', function() {
            const result = navigationService.parseGoto('#5');
            expect(result).to.deep.equal({
                type: 'step',
                flowId: null,
                step: 5
            });
        });

        it('should parse "flowId" correctly', function() {
            const result = navigationService.parseGoto('flow-123');
            expect(result).to.deep.equal({
                type: 'flow',
                flowId: 'flow-123',
                step: 0
            });
        });

        it('should parse "flowId#step" correctly', function() {
            const result = navigationService.parseGoto('flow-123#7');
            expect(result).to.deep.equal({
                type: 'flow_step',
                flowId: 'flow-123',
                step: 7
            });
        });

        it('should throw error for invalid step number', function() {
            expect(() => navigationService.parseGoto('#abc')).to.throw('Invalid step number');
        });

        it('should throw error for invalid flowId#step format', function() {
            expect(() => navigationService.parseGoto('flow#abc')).to.throw('Invalid flowId#step format');
        });

        it('should throw error for empty goto value', function() {
            expect(() => navigationService.parseGoto('')).to.throw('Invalid goto value');
        });
    });

    describe('validateGoto', function() {
        it('should validate correct goto values', function() {
            expect(navigationService.validateGoto('continue')).to.be.true;
            expect(navigationService.validateGoto('#5')).to.be.true;
            expect(navigationService.validateGoto('flow-123')).to.be.true;
            expect(navigationService.validateGoto('flow-123#7')).to.be.true;
        });

        it('should reject invalid goto values', function() {
            expect(navigationService.validateGoto('')).to.be.false;
            expect(navigationService.validateGoto('#abc')).to.be.false;
            expect(navigationService.validateGoto('flow#abc')).to.be.false;
            expect(navigationService.validateGoto(null)).to.be.false;
        });
    });

    describe('executeNavigation', function() {
        it('should continue current flow', async function() {
            const navigationAction = { type: 'continue', flowId: null, step: null };
            const result = await navigationService.executeNavigation(testDialog, navigationAction);

            expect(result.type).to.equal('message');
            expect(result.step).to.equal(1);
            expect(result.flowId).to.equal(testFlow1.id);
            expect(result.flowSwitched).to.be.false;
        });

        it('should jump to step within current flow', async function() {
            const navigationAction = { type: 'step', flowId: null, step: 5 };
            const result = await navigationService.executeNavigation(testDialog, navigationAction);

            expect(result.type).to.equal('message');
            expect(result.step).to.equal(5);
            expect(result.flowId).to.equal(testFlow1.id);
            expect(result.flowSwitched).to.be.false;
        });

        it('should jump to another flow', async function() {
            const navigationAction = { type: 'flow', flowId: testFlow2.id, step: 0 };
            const result = await navigationService.executeNavigation(testDialog, navigationAction);

            expect(result.type).to.equal('message');
            expect(result.step).to.equal(0);
            expect(result.flowId).to.equal(testFlow2.id);
            expect(result.flowSwitched).to.be.true;
        });

        it('should jump to specific step in another flow', async function() {
            const navigationAction = { type: 'flow_step', flowId: testFlow2.id, step: 3 };
            const result = await navigationService.executeNavigation(testDialog, navigationAction);

            expect(result.type).to.equal('message');
            expect(result.step).to.equal(3);
            expect(result.flowId).to.equal(testFlow2.id);
            expect(result.flowSwitched).to.be.true;
        });

        it('should complete dialog when no message found', async function() {
            const navigationAction = { type: 'step', flowId: null, step: 99 };
            const result = await navigationService.executeNavigation(testDialog, navigationAction);

            expect(result.type).to.equal('complete');
            expect(result.step).to.equal(99);
            expect(result.flowSwitched).to.be.false;
        });

        it('should throw error for non-existent flow', async function() {
            const navigationAction = { type: 'flow', flowId: 'non-existent', step: 0 };
            
            try {
                await navigationService.executeNavigation(testDialog, navigationAction);
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect(error.message).to.include('Target flow non-existent not found');
            }
        });

        it('should update dialog state correctly', async function() {
            const navigationAction = { type: 'flow', flowId: testFlow2.id, step: 0 };
            await navigationService.executeNavigation(testDialog, navigationAction);

            // Reload dialog from database
            await testDialog.reload();
            expect(testDialog.flowId).to.equal(testFlow2.id);
            expect(testDialog.currentStep).to.equal(0);
        });
    });

    describe('jumpToStep', function() {
        it('should update dialog correctly when jumping to step', async function() {
            const result = await navigationService.jumpToStep(testDialog, testFlow1.id, 5);

            expect(result.type).to.equal('message');
            expect(result.step).to.equal(5);
            expect(result.message.content).to.equal('Step 5 message');

            // Verify dialog was updated
            await testDialog.reload();
            expect(testDialog.currentStep).to.equal(5);
        });

        it('should handle flow switching correctly', async function() {
            const result = await navigationService.jumpToStep(testDialog, testFlow2.id, 3);

            expect(result.flowSwitched).to.be.true;
            expect(result.flowId).to.equal(testFlow2.id);

            // Verify dialog was updated
            await testDialog.reload();
            expect(testDialog.flowId).to.equal(testFlow2.id);
            expect(testDialog.currentStep).to.equal(3);
        });
    });
});
