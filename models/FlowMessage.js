import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';
import Flow from './Flow.js';

const FlowMessage = sequelize.define('FlowMessage', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
    },
    flowId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: Flow,
            key: 'id'
        }
    },
    content: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: 'Bot message content, may include response masks like {yes|no}'
    },
    step: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Step number in the flow sequence'
    },
    messageType: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'regular',
        validate: {
            isIn: [['regular', 'conditional_switch']]
        },
        comment: 'Type of message: regular or conditional_switch'
    },
    conditionConfig: {
        type: DataTypes.JSON,
        allowNull: true,
        comment: 'Configuration for conditional flow switching (only for conditional_switch type)'
    }
}, {
    tableName: 'flow_messages',
    timestamps: false
});

// Define the relationship between Flow and FlowMessage
Flow.hasMany(FlowMessage, {
    foreignKey: 'flowId',
    onDelete: 'CASCADE',
    hooks: true
});
FlowMessage.belongsTo(Flow, {
    foreignKey: 'flowId',
    onDelete: 'CASCADE'
});

export default FlowMessage;