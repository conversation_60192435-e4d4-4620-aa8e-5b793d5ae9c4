import {DataTypes} from 'sequelize';
import {sequelize} from '../db.js';

const Flow = sequelize.define('Flow', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
        allowNull: false
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    aiPrompt: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Prompt for AI to evaluate dialog completion'
    },
    successMessage: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: 'Спасибо за предоставленную информацию. Пожалуйста, возьмите талон и ожидайте вызова к окну оператора.',
        comment: 'Message shown when dialog completes successfully'
    },
    failureMessage: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: 'К сожалению, вы не соответствуете требованиям для получения услуги. Пожалуйста, убедитесь, что у вас есть все необходимые документы и соответствие требованиям.',
        comment: 'Message shown when dialog fails'
    },
    isStartingFlow: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether this flow can be used as an initial/starting flow'
    },
    createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    },
    updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: 'flows',
    timestamps: true
});

export default Flow;